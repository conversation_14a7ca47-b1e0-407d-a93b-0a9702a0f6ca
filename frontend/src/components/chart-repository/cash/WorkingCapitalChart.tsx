import React from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Label,
  Legend,
  Line,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface WorkingCapitalChartProps {
  data: {
    months: string[]
    components: {
      name: string
      data: number[]
      color: string
    }[]
    workingCapitalPercent: number[]
    industryBenchmark: number
    targetPercent: number
    kpis: {
      name: string
      value: string | number
      change: number
      trend: string
      status: string
    }[]
  }
}

interface ChartDataPoint {
  month: string
  [key: string]: string | number | undefined
  workingCapitalPercent?: number
}

const WorkingCapitalChart: React.FC<WorkingCapitalChartProps> = ({ data }) => {
  // Transform data for trend view
  const transformTrendData = (): ChartDataPoint[] => {
    return data.months.map((month, index) => {
      const dataPoint: ChartDataPoint = { month }

      // Add component data
      data.components.forEach((component) => {
        dataPoint[component.name] = component.data[index]
      })

      // Add working capital percent
      dataPoint.workingCapitalPercent = data.workingCapitalPercent[index]

      return dataPoint
    })
  }

  const trendData = transformTrendData()

  // Custom tooltip for trend
  const TrendTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <p className="font-semibold">Month: {label}</p>
          {payload.map((entry: any, index: number) => {
            if (entry.dataKey === "workingCapitalPercent") {
              return (
                <p key={`item-${index}`} style={{ color: entry.color }}>
                  Working Capital % of Revenue: {entry.value.toFixed(1)}%
                </p>
              )
            }

            return (
              <p key={`item-${index}`} style={{ color: entry.color }}>
                {entry.name}: {formatAbbreviatedCurrency(entry.value, 0)}
              </p>
            )
          })}
        </div>
      )
    }
    return null
  }

  return (
    <div className="flex flex-col">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={trendData} margin={{ right: 40 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis
            yAxisId="left"
            tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            tickFormatter={(value) => `${value}%`}
            tick={{ fontSize: 12 }}
          />
          <Tooltip content={<TrendTooltip />} />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 8 }}
          />

          {/* Stacked bars for components */}
          {data.components.map((component, index) => (
            <Bar
              key={`bar-${index}`}
              dataKey={component.name}
              stackId="1"
              fill={component.color}
              yAxisId="left"
            />
          ))}

          {/* Industry benchmark reference line */}
          <ReferenceLine
            yAxisId="right"
            y={data.industryBenchmark}
            stroke="#E53E3E"
            strokeDasharray="5 5"
          >
            <Label
              value={`Industry (${data.industryBenchmark}%)`}
              position="right"
              fill="#E53E3E"
              fontSize={12}
            />
          </ReferenceLine>

          {/* Target percent reference line */}
          <ReferenceLine
            yAxisId="right"
            y={data.targetPercent}
            stroke="#ED8936"
            strokeDasharray="5 5"
          >
            <Label
              value={`Target (${data.targetPercent}%)`}
              position="right"
              fill="#ED8936"
              fontSize={12}
            />
          </ReferenceLine>

          {/* Working capital percent line */}
          <Line
            type="monotone"
            dataKey="workingCapitalPercent"
            name="Working Capital % of Revenue"
            stroke="#2F3E9E"
            strokeWidth={2}
            yAxisId="right"
          />
        </ComposedChart>
      </ResponsiveContainer>

      {/* KPI Cards */}
      <div className="mt-2 grid grid-cols-4 gap-2">
        {data.kpis.map((kpi, i) => (
          <div
            key={i}
            className="rounded border border-gray-200 bg-white p-2 shadow-sm"
          >
            <div className="text-xs text-gray-500">{kpi.name}</div>
            <div className="flex items-center justify-between">
              <div className="text-lg font-semibold">{kpi.value}</div>
              <div
                className={`flex items-center text-xs ${
                  kpi.status === "positive"
                    ? "text-[#48BB78]"
                    : kpi.status === "negative"
                      ? "text-[#E53E3E]"
                      : "text-gray-600"
                }`}
              >
                {kpi.change > 0 ? "+" : ""}
                {kpi.change}%
                {kpi.trend === "up" ? (
                  <svg
                    className="ml-1 h-3 w-3"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="ml-1 h-3 w-3"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default WorkingCapitalChart
