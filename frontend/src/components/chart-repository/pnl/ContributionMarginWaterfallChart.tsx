"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  LabelList,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"
import { contributionMarginData } from "@/components/chart-repository/data/contributionMarginData"

const ContributionMarginWaterfallChart = ({
  data,
}: {
  data: typeof contributionMarginData
}) => {
  const formattedData = data.units.map((unit) => ({
    name: unit.unit,
    revenue: unit.revenue,
    directCosts: unit.directCosts,
    contributionMargin: unit.contributionMargin,
    contributionMarginPercent: unit.contributionMarginPercent,
  }))

  const formatYAxis = (value: number) => {
    if (value >= 1_000_000) return `$${(value / 1_000_000).toFixed(1)}M`
    if (value >= 1_000) return `$${(value / 1_000).toFixed(0)}K`
    return `$${value}`
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const revenue =
        payload.find((p: any) => p.dataKey === "revenue")?.value || 0
      const directCosts =
        payload.find((p: any) => p.dataKey === "directCosts")?.value || 0
      const contributionMargin =
        payload.find((p: any) => p.dataKey === "contributionMargin")?.value || 0
      const marginPercent = ((contributionMargin / revenue) * 100).toFixed(1)

      return (
        <div className="rounded border border-gray-300 bg-white p-3 shadow-lg">
          <p className="mb-2 text-lg font-bold text-gray-900">{label}</p>
          <div className="space-y-1 text-sm text-gray-800">
            <p>
              Revenue:{" "}
              <span className="font-bold">${revenue.toLocaleString()}</span>
            </p>
            <p>
              Direct Costs:{" "}
              <span className="font-bold text-red-600">
                ${directCosts.toLocaleString()}
              </span>
            </p>
            <p>
              Contribution Margin:{" "}
              <span className="font-bold text-green-600">
                ${contributionMargin.toLocaleString()}
              </span>
            </p>
            <p>
              Margin %:{" "}
              <span
                className={`font-bold ${Number(marginPercent) >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                {marginPercent}%
              </span>
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  const renderCustomizedLabel = ({ x, y, width, index }: any) => {
    const item = formattedData[index]
    const percent = item.contributionMarginPercent
    const isPositive = percent >= 0
    const bubbleX = x + width / 2
    const bubbleY = y - 20

    return (
      <g>
        <circle
          cx={bubbleX}
          cy={bubbleY}
          r={16}
          fill={isPositive ? "#4CAF50" : "#F44336"}
        />
        <text
          x={bubbleX}
          y={bubbleY}
          textAnchor="middle"
          dominantBaseline="middle"
          fill="white"
          fontSize="10"
          fontWeight="bold"
        >
          {isPositive ? "+" : ""}
          {percent.toFixed(1)}%
        </text>
      </g>
    )
  }

  return (
    <div className="flex flex-col">
      {/* Custom Legend */}
      <div className="mt-2 mb-4 flex justify-center space-x-6">
        <div className="flex items-center">
          <div className="mr-1 h-3 w-3 bg-blue-400" />
          <span className="text-xs font-medium text-gray-900">Revenue</span>
        </div>
        <div className="flex items-center">
          <div className="mr-1 h-3 w-3 bg-red-500" />
          <span className="text-xs font-medium text-gray-900">
            Direct Costs
          </span>
        </div>
        <div className="flex items-center">
          <div className="mr-1 h-3 w-3 bg-blue-900" />
          <span className="text-xs font-medium text-gray-900">
            Contribution Margin
          </span>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <BarChart
          data={formattedData}
          barGap={0}
          barCategoryGap={30}
          margin={{ top: 30, right: 30, left: 40, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" tick={{ fontSize: 12, fill: "#000" }} />
          <YAxis
            tickFormatter={formatYAxis}
            domain={[0, (max: number) => max * 1.2]}
            tick={{ fontSize: 12, fill: "#000" }}
            width={50}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="revenue" name="Revenue" fill="#90CAF9" />
          <Bar dataKey="directCosts" name="Direct Costs" fill="#F44336" />
          <Bar
            dataKey="contributionMargin"
            name="Contribution Margin"
            fill="#0D47A1"
          >
            <LabelList
              dataKey="contributionMarginPercent"
              position="top"
              content={renderCustomizedLabel}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

export default ContributionMarginWaterfallChart
