export const cashForecastData = {
  weeks: [
    "Week 1",
    "Week 2",
    "Week 3",
    "Week 4",
    "Week 5",
    "Week 6",
    "Week 7",
    "Week 8",
    "Week 9",
    "Week 10",
    "Week 11",
    "Week 12",
    "Week 13",
  ],
  segments: [
    {
      name: "Aesthetics",
      color: "#38A169",
      data: [
        180000, 185000, 190000, 195000, 200000, 205000, 210000, 215000, 220000,
        225000, 230000, 235000, 240000,
      ],
    },
    {
      name: "Corporate",
      color: "#4299E1",
      data: [
        220000, 225000, 230000, 235000, 240000, 245000, 250000, 255000, 260000,
        265000, 270000, 275000, 280000,
      ],
    },
    {
      name: "Imaging",
      color: "#805AD5",
      data: [
        160000, 165000, 170000, 175000, 180000, 185000, 190000, 195000, 200000,
        205000, 210000, 215000, 220000,
      ],
    },
    {
      name: "Oncology",
      color: "#E53E3E",
      data: [
        280000, 285000, 290000, 295000, 300000, 305000, 310000, 315000, 320000,
        325000, 330000, 335000, 340000,
      ],
    },
    {
      name: "Paediatrics",
      color: "#ED8936",
      data: [
        150000, 155000, 160000, 165000, 170000, 175000, 180000, 185000, 190000,
        195000, 200000, 205000, 210000,
      ],
    },
    {
      name: "Women's Health",
      color: "#D69E2E",
      data: [
        200000, 205000, 210000, 215000, 220000, 225000, 230000, 235000, 240000,
        245000, 250000, 255000, 260000,
      ],
    },
    {
      name: "Others",
      color: "#718096",
      data: [
        50000, 52000, 54000, 56000, 58000, 60000, 62000, 64000, 66000, 68000,
        70000, 72000, 74000,
      ],
    },
  ],
  actual: {
    name: "Actual",
    segments: [
      {
        name: "Aesthetics",
        data: [
          180000,
          188000,
          192000,
          198000,
          202000,
          208000,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
      },
      {
        name: "Corporate",
        data: [
          220000,
          228000,
          232000,
          238000,
          242000,
          248000,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
      },
      {
        name: "Imaging",
        data: [
          160000,
          168000,
          172000,
          178000,
          182000,
          188000,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
      },
      {
        name: "Oncology",
        data: [
          280000,
          288000,
          292000,
          298000,
          302000,
          308000,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
      },
      {
        name: "Paediatrics",
        data: [
          150000,
          158000,
          162000,
          168000,
          172000,
          178000,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
      },
      {
        name: "Women's Health",
        data: [
          200000,
          208000,
          212000,
          218000,
          222000,
          228000,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
      },
      {
        name: "Others",
        data: [
          50000,
          52000,
          54000,
          56000,
          58000,
          60000,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
      },
    ],
  },
}
