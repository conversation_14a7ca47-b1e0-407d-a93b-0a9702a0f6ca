import React from "react"
import {
  CartesianGrid,
  Cell,
  ReferenceLine,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON>hart,
  Tooltip,
  XAxis,
  YAxis,
  <PERSON><PERSON>xis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

interface BusinessUnitGrowthChartProps {
  data: {
    revenue: Array<{
      month: string
      primary: number
      specialty: number
      urgent: number
      diagnostics: number
      surgery: number
      pt: number
      mental: number
    }>
    patientVolume: Array<{
      month: string
      primary: number
      specialty: number
      urgent: number
      diagnostics: number
      surgery: number
      pt: number
      mental: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const BusinessUnitGrowthChart: React.FC<BusinessUnitGrowthChartProps> = ({
  data,
}) => {
  // Calculate growth rates for each business unit
  const units = data.businessUnits.filter((unit) => unit.id !== "admin")

  const growthData = units.map((unit) => {
    const unitId = unit.id

    // Get first and last month data for revenue and patient volume
    const firstMonthRevenue = data.revenue[0][
      unitId as keyof (typeof data.revenue)[0]
    ] as number
    const lastMonthRevenue = data.revenue[data.revenue.length - 1][
      unitId as keyof (typeof data.revenue)[0]
    ] as number

    const firstMonthVolume = data.patientVolume[0][
      unitId as keyof (typeof data.patientVolume)[0]
    ] as number
    const lastMonthVolume = data.patientVolume[data.patientVolume.length - 1][
      unitId as keyof (typeof data.patientVolume)[0]
    ] as number

    // Calculate growth percentages
    const revenueGrowth =
      ((lastMonthRevenue - firstMonthRevenue) / firstMonthRevenue) * 100
    const volumeGrowth =
      ((lastMonthVolume - firstMonthVolume) / firstMonthVolume) * 100

    // Calculate average revenue per patient
    const revenuePerPatient = lastMonthRevenue / lastMonthVolume

    return {
      unit: unit.name,
      revenueGrowth: parseFloat(revenueGrowth.toFixed(1)),
      volumeGrowth: parseFloat(volumeGrowth.toFixed(1)),
      revenuePerPatient: Math.round(revenuePerPatient),
      color: unit.color,
      totalRevenue: lastMonthRevenue,
    }
  })

  return (
    <div className="flex flex-col gap-4">
      <div className="mt-2 flex flex-wrap justify-center gap-x-4 gap-y-2">
        {growthData.map((entry, index) => (
          <div key={index} className="flex items-center gap-1.5">
            <div
              className="size-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-xs text-gray-600">{entry.unit}</span>
          </div>
        ))}
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ScatterChart margin={{ bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            type="number"
            dataKey="volumeGrowth"
            name="Patient Volume Growth"
            domain={["dataMin - 2", "dataMax + 2"]}
            label={{
              value: "Patient Volume Growth (%)",
              position: "bottom",
              fontSize: 12,
            }}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            type="number"
            dataKey="revenueGrowth"
            name="Revenue Growth"
            domain={["dataMin - 2", "dataMax + 2"]}
            label={{
              value: "Revenue Growth (%)",
              angle: -90,
              position: "insideLeft",
              fontSize: 12,
            }}
            tick={{ fontSize: 12 }}
          />
          <ZAxis
            type="number"
            dataKey="revenuePerPatient"
            range={[450, 3600]}
            name="Revenue per Patient"
          />
          <Tooltip
            formatter={(value, name, props) => {
              if (name === "Patient Volume Growth") return [`${value}%`, name]
              if (name === "Revenue Growth") return [`${value}%`, name]
              if (name === "Revenue per Patient") return [`$${value}`, name]
              return [value, name]
            }}
            labelFormatter={(label, payload) => {
              if (payload && payload.length > 0) {
                return `Unit: ${payload[0].payload.unit}`
              }
              return label
            }}
            cursor={{ strokeDasharray: "3 3" }}
            contentStyle={{ fontSize: "12px" }}
          />
          <ReferenceLine x={0} stroke="#000" strokeDasharray="3 3" />
          <ReferenceLine y={0} stroke="#000" strokeDasharray="3 3" />
          <Scatter name="Business Units" data={growthData} fill="#8884d8">
            {growthData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Scatter>
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  )
}

export default BusinessUnitGrowthChart
