// Data for business unit performance metrics for a medical group
export const businessUnitPerformanceData = {
  // Business units in our medical group
  businessUnits: [
    { id: "primary", name: "Primary Care", color: "#4299E1" },
    { id: "specialty", name: "Specialty Care", color: "#48BB78" },
    { id: "urgent", name: "Urgent Care", color: "#ED8936" },
    { id: "diagnostics", name: "Diagnostics", color: "#9F7AEA" },
    { id: "surgery", name: "Surgery", color: "#F56565" },
    { id: "pt", name: "Physical Therapy", color: "#38B2AC" },
    { id: "mental", name: "Mental Health", color: "#ECC94B" },
    { id: "admin", name: "Administration", color: "#A0AEC0" },
  ],

  // 1. Patient Volume by Business Unit (monthly trend)
  patientVolume: [
    {
      month: "Jan",
      primary: 3200,
      specialty: 1800,
      urgent: 2100,
      diagnostics: 1500,
      surgery: 420,
      pt: 980,
      mental: 750,
    },
    {
      month: "Feb",
      primary: 3150,
      specialty: 1750,
      urgent: 1950,
      diagnostics: 1450,
      surgery: 390,
      pt: 920,
      mental: 780,
    },
    {
      month: "Mar",
      primary: 3300,
      specialty: 1900,
      urgent: 2050,
      diagnostics: 1600,
      surgery: 450,
      pt: 1050,
      mental: 820,
    },
    {
      month: "Apr",
      primary: 3400,
      specialty: 2000,
      urgent: 2200,
      diagnostics: 1650,
      surgery: 480,
      pt: 1100,
      mental: 850,
    },
    {
      month: "May",
      primary: 3450,
      specialty: 2100,
      urgent: 2300,
      diagnostics: 1700,
      surgery: 510,
      pt: 1150,
      mental: 900,
    },
    {
      month: "Jun",
      primary: 3300,
      specialty: 2050,
      urgent: 2250,
      diagnostics: 1650,
      surgery: 490,
      pt: 1120,
      mental: 930,
    },
  ],

  // 2. Revenue by Business Unit
  revenue: [
    {
      month: "Jan",
      primary: 640000,
      specialty: 720000,
      urgent: 420000,
      diagnostics: 375000,
      surgery: 840000,
      pt: 196000,
      mental: 187500,
    },
    {
      month: "Feb",
      primary: 630000,
      specialty: 700000,
      urgent: 390000,
      diagnostics: 362500,
      surgery: 780000,
      pt: 184000,
      mental: 195000,
    },
    {
      month: "Mar",
      primary: 660000,
      specialty: 760000,
      urgent: 410000,
      diagnostics: 400000,
      surgery: 900000,
      pt: 210000,
      mental: 205000,
    },
    {
      month: "Apr",
      primary: 680000,
      specialty: 800000,
      urgent: 440000,
      diagnostics: 412500,
      surgery: 960000,
      pt: 220000,
      mental: 212500,
    },
    {
      month: "May",
      primary: 690000,
      specialty: 840000,
      urgent: 460000,
      diagnostics: 425000,
      surgery: 1020000,
      pt: 230000,
      mental: 225000,
    },
    {
      month: "Jun",
      primary: 660000,
      specialty: 820000,
      urgent: 450000,
      diagnostics: 412500,
      surgery: 980000,
      pt: 224000,
      mental: 232500,
    },
  ],

  // 3. Profitability Metrics
  profitability: [
    {
      unit: "Primary Care",
      revenue: 3960000,
      expenses: 3168000,
      profit: 792000,
      margin: 20,
    },
    {
      unit: "Specialty Care",
      revenue: 4640000,
      expenses: 3248000,
      profit: 1392000,
      margin: 30,
    },
    {
      unit: "Urgent Care",
      revenue: 2570000,
      expenses: 2056000,
      profit: 514000,
      margin: 20,
    },
    {
      unit: "Diagnostics",
      revenue: 2387500,
      expenses: 1671250,
      profit: 716250,
      margin: 30,
    },
    {
      unit: "Surgery",
      revenue: 5480000,
      expenses: 4384000,
      profit: 1096000,
      margin: 20,
    },
    {
      unit: "Physical Therapy",
      revenue: 1264000,
      expenses: 885800,
      profit: 378200,
      margin: 30,
    },
    {
      unit: "Mental Health",
      revenue: 1257500,
      expenses: 1006000,
      profit: 251500,
      margin: 20,
    },
    {
      unit: "Administration",
      revenue: 0,
      expenses: 1200000,
      profit: -1200000,
      margin: -100,
    },
  ],

  // 4. Patient Satisfaction Scores
  patientSatisfaction: [
    { unit: "Primary Care", score: 4.2, benchmark: 4.0, responses: 1250 },
    { unit: "Specialty Care", score: 4.5, benchmark: 4.2, responses: 980 },
    { unit: "Urgent Care", score: 3.8, benchmark: 3.9, responses: 1100 },
    { unit: "Diagnostics", score: 4.3, benchmark: 4.1, responses: 850 },
    { unit: "Surgery", score: 4.7, benchmark: 4.3, responses: 420 },
    { unit: "Physical Therapy", score: 4.6, benchmark: 4.4, responses: 560 },
    { unit: "Mental Health", score: 4.4, benchmark: 4.2, responses: 390 },
  ],

  // 5. Provider Productivity
  providerProductivity: [
    {
      unit: "Primary Care",
      patientsPerDay: 22,
      revenuePerPatient: 200,
      benchmark: 20,
    },
    {
      unit: "Specialty Care",
      patientsPerDay: 16,
      revenuePerPatient: 400,
      benchmark: 15,
    },
    {
      unit: "Urgent Care",
      patientsPerDay: 28,
      revenuePerPatient: 200,
      benchmark: 25,
    },
    {
      unit: "Diagnostics",
      patientsPerDay: 18,
      revenuePerPatient: 250,
      benchmark: 20,
    },
    {
      unit: "Surgery",
      patientsPerDay: 8,
      revenuePerPatient: 2000,
      benchmark: 8,
    },
    {
      unit: "Physical Therapy",
      patientsPerDay: 12,
      revenuePerPatient: 200,
      benchmark: 12,
    },
    {
      unit: "Mental Health",
      patientsPerDay: 10,
      revenuePerPatient: 250,
      benchmark: 10,
    },
  ],

  // 6. Quality Metrics
  qualityMetrics: [
    {
      unit: "Primary Care",
      preventiveScreening: 82,
      chronicDiseaseManagement: 78,
      readmissionRate: 4.2,
    },
    {
      unit: "Specialty Care",
      preventiveScreening: 75,
      chronicDiseaseManagement: 85,
      readmissionRate: 3.8,
    },
    {
      unit: "Urgent Care",
      preventiveScreening: 45,
      chronicDiseaseManagement: 50,
      readmissionRate: 6.5,
    },
    {
      unit: "Diagnostics",
      preventiveScreening: 90,
      chronicDiseaseManagement: 65,
      readmissionRate: 2.1,
    },
    {
      unit: "Surgery",
      preventiveScreening: 65,
      chronicDiseaseManagement: 70,
      readmissionRate: 5.2,
    },
    {
      unit: "Physical Therapy",
      preventiveScreening: 60,
      chronicDiseaseManagement: 88,
      readmissionRate: 1.8,
    },
    {
      unit: "Mental Health",
      preventiveScreening: 72,
      chronicDiseaseManagement: 82,
      readmissionRate: 3.5,
    },
  ],

  // 7. Referral Patterns
  referralPatterns: [
    { source: "Primary Care", target: "Specialty Care", value: 850 },
    { source: "Primary Care", target: "Diagnostics", value: 720 },
    { source: "Primary Care", target: "Physical Therapy", value: 480 },
    { source: "Primary Care", target: "Mental Health", value: 320 },
    { source: "Urgent Care", target: "Primary Care", value: 560 },
    { source: "Urgent Care", target: "Specialty Care", value: 390 },
    { source: "Specialty Care", target: "Surgery", value: 410 },
    { source: "Specialty Care", target: "Diagnostics", value: 680 },
    { source: "Specialty Care", target: "Physical Therapy", value: 290 },
    { source: "Mental Health", target: "Specialty Care", value: 180 },
    { source: "Physical Therapy", target: "Specialty Care", value: 120 },
  ],

  // 7a. Insurance Referral Data (monthly trend for line chart)
  insuranceReferrals: [
    { source: "Primary Care", insuranceReferrals: 450 },
    { source: "Specialty Care", insuranceReferrals: 380 },
    { source: "Urgent Care", insuranceReferrals: 290 },
    { source: "Diagnostics", insuranceReferrals: 220 },
    { source: "Surgery", insuranceReferrals: 180 },
    { source: "Physical Therapy", insuranceReferrals: 80 },
    { source: "Mental Health", insuranceReferrals: 120 },
  ],

  // 8. Operational Metrics
  operationalMetrics: [
    { unit: "Primary Care", waitTime: 12, noShowRate: 8.5, roomTurnover: 15 },
    { unit: "Specialty Care", waitTime: 18, noShowRate: 7.2, roomTurnover: 22 },
    { unit: "Urgent Care", waitTime: 25, noShowRate: 4.8, roomTurnover: 18 },
    { unit: "Diagnostics", waitTime: 15, noShowRate: 6.5, roomTurnover: 25 },
    { unit: "Surgery", waitTime: 10, noShowRate: 2.1, roomTurnover: 45 },
    {
      unit: "Physical Therapy",
      waitTime: 8,
      noShowRate: 12.4,
      roomTurnover: 20,
    },
    { unit: "Mental Health", waitTime: 14, noShowRate: 15.8, roomTurnover: 10 },
  ],

  // 9. Payer Mix
  payerMix: [
    {
      unit: "Primary Care",
      commercial: 45,
      medicare: 30,
      medicaid: 20,
      selfPay: 5,
    },
    {
      unit: "Specialty Care",
      commercial: 55,
      medicare: 25,
      medicaid: 15,
      selfPay: 5,
    },
    {
      unit: "Urgent Care",
      commercial: 40,
      medicare: 15,
      medicaid: 30,
      selfPay: 15,
    },
    {
      unit: "Diagnostics",
      commercial: 50,
      medicare: 35,
      medicaid: 10,
      selfPay: 5,
    },
    { unit: "Surgery", commercial: 60, medicare: 30, medicaid: 5, selfPay: 5 },
    {
      unit: "Physical Therapy",
      commercial: 65,
      medicare: 20,
      medicaid: 10,
      selfPay: 5,
    },
    {
      unit: "Mental Health",
      commercial: 35,
      medicare: 25,
      medicaid: 30,
      selfPay: 10,
    },
  ],

  // 10. Staff Utilization
  staffUtilization: [
    { unit: "Primary Care", physicians: 85, nurses: 78, adminStaff: 65 },
    { unit: "Specialty Care", physicians: 82, nurses: 75, adminStaff: 60 },
    { unit: "Urgent Care", physicians: 90, nurses: 88, adminStaff: 70 },
    { unit: "Diagnostics", physicians: 75, nurses: 70, adminStaff: 65 },
    { unit: "Surgery", physicians: 72, nurses: 85, adminStaff: 60 },
    { unit: "Physical Therapy", physicians: 88, nurses: 75, adminStaff: 62 },
    { unit: "Mental Health", physicians: 80, nurses: 72, adminStaff: 68 },
  ],

  // 11. Revenue vs Utilization Data (for dual-axis chart)
  revenueVsUtilization: [
    { unit: "Primary Care", revenue: 3960000, utilizationRate: 82.5 },
    { unit: "Specialty Care", revenue: 4640000, utilizationRate: 78.8 },
    { unit: "Urgent Care", revenue: 2570000, utilizationRate: 89.2 },
    { unit: "Diagnostics", revenue: 2387500, utilizationRate: 73.5 },
    { unit: "Surgery", revenue: 5480000, utilizationRate: 76.8 },
    { unit: "Physical Therapy", revenue: 1264000, utilizationRate: 85.2 },
    { unit: "Mental Health", revenue: 1257500, utilizationRate: 79.3 },
  ],
}
