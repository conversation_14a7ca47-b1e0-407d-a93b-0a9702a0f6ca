import React from "react"
import { ResponsiveSankey } from "@nivo/sankey"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

// For input data from the application
interface InputNode {
  name: string
  id?: string
  nodeColor?: string
  [key: string]: any
}

interface InputLink {
  source: string | InputNode
  target: string | InputNode
  value: number
  color?: string
  [key: string]: any
}

const CashFlowSankeyChart = ({
  data,
}: {
  data: {
    nodes: InputNode[]
    links: InputLink[]
  }
}) => {
  // Format currency for tooltip
  const formatCurrency = (value: number) => formatAbbreviatedCurrency(value, 0)

  // Transform data to match @nivo/sankey format if needed
  const transformData = () => {
    // Map nodes to ensure they have the required 'id' property
    const nodes = data.nodes.map((node) => ({
      id: node.id || node.name,
    }))

    // Map links to ensure they have the required source/target as strings
    const links = data.links.map((link) => ({
      source:
        typeof link.source === "string"
          ? link.source
          : link.source.id || link.source.name,
      target:
        typeof link.target === "string"
          ? link.target
          : link.target.id || link.target.name,
      value: Math.abs(link.value), // Sankey diagrams typically use absolute values
    }))

    return { nodes, links }
  }

  const sankeyData = transformData()

  // Calculate cash flows for a specific node
  const calculateNodeCashFlows = (nodeId: string) => {
    const originalLinks = data.links

    // Cash in: positive values flowing TO this node
    const cashIn = originalLinks
      .filter((link) => {
        const targetId =
          typeof link.target === "string"
            ? link.target
            : link.target.id || link.target.name
        return targetId === nodeId && link.value > 0
      })
      .reduce((sum, link) => sum + link.value, 0)

    // Cash out: negative values flowing FROM this node (make positive for display)
    const cashOut = originalLinks
      .filter((link) => {
        const sourceId =
          typeof link.source === "string"
            ? link.source
            : link.source.id || link.source.name
        return sourceId === nodeId && link.value < 0
      })
      .reduce((sum, link) => sum + Math.abs(link.value), 0)

    // Net change: cash in minus cash out
    const netChange = cashIn - cashOut

    return { cashIn, cashOut, netChange }
  }

  return (
    <div className="flex w-full flex-col" style={{ height: CHART_HEIGHT }}>
      <ResponsiveSankey
        data={sankeyData}
        align="justify"
        colors={{ scheme: "category10" }}
        margin={{ top: 40, right: 100, bottom: 40, left: 100 }}
        nodeOpacity={1}
        nodeThickness={15}
        nodeSpacing={10}
        nodeBorderWidth={0}
        nodeBorderColor={{ from: "color", modifiers: [["darker", 0.8]] }}
        linkOpacity={0.5}
        linkContract={1}
        enableLinkGradient={true}
        labelPosition="outside"
        labelOrientation="horizontal"
        labelPadding={16}
        labelTextColor={{ from: "color", modifiers: [["darker", 1]] }}
        nodeTooltip={({ node }: { node: any }) => {
          const { cashIn, cashOut, netChange } = calculateNodeCashFlows(node.id)

          return (
            <div className="max-w-[320px] min-w-[280px] rounded-lg border border-gray-300 bg-white p-4 text-sm shadow-lg">
              <div className="mb-3 border-b border-gray-200 pb-2 font-bold text-gray-800">
                {node.id}
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-green-500"></div>
                    <span className="font-medium text-gray-700">Cash In:</span>
                  </div>
                  <span className="font-semibold text-green-600">
                    {formatCurrency(cashIn)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-red-500"></div>
                    <span className="font-medium text-gray-700">Cash Out:</span>
                  </div>
                  <span className="font-semibold text-red-600">
                    {formatCurrency(cashOut)}
                  </span>
                </div>
                <div className="flex items-center justify-between border-t border-gray-200 pt-3">
                  <div className="flex items-center gap-2">
                    <div
                      className={`h-3 w-3 rounded-full ${netChange >= 0 ? "bg-green-500" : "bg-red-500"}`}
                    ></div>
                    <span className="font-semibold text-gray-800">
                      Net Change:
                    </span>
                  </div>
                  <span
                    className={`text-base font-bold ${netChange >= 0 ? "text-green-600" : "text-red-600"}`}
                  >
                    {netChange >= 0 ? "+" : ""}
                    {formatCurrency(netChange)}
                  </span>
                </div>
              </div>
            </div>
          )
        }}
        linkTooltip={({ link }: { link: any }) => (
          <div className="max-w-[280px] min-w-[240px] rounded-lg border border-gray-300 bg-white p-4 text-sm shadow-lg">
            <div className="mb-3 border-b border-gray-200 pb-2 font-bold text-gray-800">
              Cash Flow
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-700">From:</span>
                <span className="font-semibold text-gray-800">
                  {link.source.id}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-700">To:</span>
                <span className="font-semibold text-gray-800">
                  {link.target.id}
                </span>
              </div>
              <div className="flex items-center justify-between border-t border-gray-200 pt-2">
                <span className="font-semibold text-gray-800">Amount:</span>
                <span className="text-base font-bold text-blue-600">
                  {formatCurrency(link.value)}
                </span>
              </div>
            </div>
          </div>
        )}
      />
    </div>
  )
}

export default CashFlowSankeyChart
