import React from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XA<PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface BusinessUnitProfitabilityChartProps {
  data: {
    profitability: Array<{
      unit: string
      revenue: number
      expenses: number
      profit: number
      margin: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const BusinessUnitProfitabilityChart: React.FC<
  BusinessUnitProfitabilityChartProps
> = ({ data }) => {
  // Filter out admin for the chart
  const chartData = data.profitability.filter(
    (item) => item.unit !== "Administration"
  )

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="unit" tick={{ fontSize: 12 }} />
        <YAxis
          yAxisId="left"
          tickFormatter={(value) => `$${formatAbbreviatedCurrency(value, 0)}`}
          label={{
            value: "Amount ($)",
            angle: -90,
            position: "insideLeft",
            fontSize: 12,
          }}
          tick={{ fontSize: 12 }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          domain={[0, 40]}
          tickFormatter={(value) => `${value}%`}
          label={{
            value: "Margin (%)",
            angle: 90,
            position: "insideRight",
            fontSize: 12,
          }}
          tick={{ fontSize: 12 }}
        />
        <Tooltip
          formatter={(value, name) => {
            if (name === "Margin") return [`${value}%`, name]
            return [`$${value.toLocaleString()}`, name]
          }}
        />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar yAxisId="left" dataKey="revenue" name="Revenue" fill="#8884d8" />
        <Bar
          yAxisId="left"
          dataKey="expenses"
          name="Expenses"
          fill="#82ca9d"
          opacity={0.7}
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="margin"
          name="Margin"
          stroke="#ff7300"
          strokeWidth={2}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default BusinessUnitProfitabilityChart
