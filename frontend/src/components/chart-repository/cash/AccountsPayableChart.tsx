import React from "react"
import { format } from "date-fns"
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ComposedChart,
  Legend,
  ReferenceArea,
  ReferenceLine,
  ResponsiveContainer,
  Scatter,
  ScatterChart,
  Tooltip,
  XAxis,
  YAxis,
  ZAxis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

interface AccountsPayableChartProps {
  data: {
    schedule: {
      vendor: string
      invoiceNumber: string
      amount: number
      dueDate: string
      category: string
      priority: string
      startDate: string
      endDate: string
    }[]
    priorityColors: {
      [key: string]: string
    }
    categoryColors: {
      [key: string]: string
    }
  }
}

const AccountsPayableChart: React.FC<AccountsPayableChartProps> = ({
  data,
}) => {
  // Sort data by due date
  const sortedData = [...data.schedule].sort(
    (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
  )

  // Transform data for Recharts
  const chartData = sortedData.map((item, index) => ({
    vendor: item.vendor,
    invoiceNumber: item.invoiceNumber,
    amount: item.amount,
    dueDate: new Date(item.dueDate),
    category: item.category,
    priority: item.priority,
    startDate: new Date(item.startDate),
    endDate: new Date(item.endDate),
    startTimestamp: new Date(item.startDate).getTime(),
    endTimestamp: new Date(item.endDate).getTime(),
    durationMs:
      new Date(item.endDate).getTime() - new Date(item.startDate).getTime(),
    yIndex: index, // Numeric index for y-axis
  }))

  const today = new Date()
  const todayTimestamp = today.getTime()

  // Tooltip for timeline chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="rounded border border-gray-200 bg-white p-2 shadow">
          <div>
            <strong>Vendor:</strong> {data.vendor}
          </div>
          <div>
            <strong>Invoice:</strong> {data.invoiceNumber}
          </div>
          <div>
            <strong>Amount:</strong> ${data.amount.toLocaleString()}
          </div>
          <div>
            <strong>Due Date:</strong> {format(data.dueDate, "MMM dd, yyyy")}
          </div>
          <div>
            <strong>Category:</strong> {data.category}
          </div>
          <div>
            <strong>Priority:</strong> {data.priority}
          </div>
        </div>
      )
    }
    return null
  }

  const formatXAxis = (timestamp: number) => {
    return format(new Date(timestamp), "MMM dd")
  }

  const renderLegend = () => {
    const priorities = Object.keys(data.priorityColors)

    return (
      <div className="mt-2 flex flex-wrap justify-center gap-4">
        {priorities.map((priority) => (
          <div key={priority} className="flex items-center">
            <div
              className="mr-1 h-3 w-3"
              style={{ backgroundColor: data.priorityColors[priority] }}
            />
            <span className="text-xs">{priority}</span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="flex flex-col">
      {/* Timeline Chart */}
      <ResponsiveContainer width="100%" height={CHART_HEIGHT / 2}>
        <BarChart
          data={chartData.map((d) => ({
            ...d,
            startOffset:
              (d.startTimestamp -
                Math.min(...chartData.map((i) => i.startTimestamp))) /
              (1000 * 60 * 60 * 24),
            durationDays: d.durationMs / (1000 * 60 * 60 * 24),
          }))}
          layout="vertical"
        >
          <CartesianGrid strokeDasharray="3 3" horizontal={false} />
          <XAxis
            type="number"
            tickFormatter={(value) =>
              format(
                new Date(
                  Math.min(...chartData.map((d) => d.startTimestamp)) +
                    value * 24 * 60 * 60 * 1000
                ),
                "MMM dd"
              )
            }
            domain={["dataMin", "dataMax"]}
            tick={{ fontSize: 12 }}
          />
          <YAxis type="category" dataKey="vendor" tick={{ fontSize: 12 }} />
          <Tooltip content={<CustomTooltip />} />
          <Legend
            content={renderLegend}
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          {/* Invisible offset bar */}
          <Bar dataKey="startOffset" stackId="a" fill="transparent" />
          {/* Actual duration bar */}
          <Bar
            dataKey="durationDays"
            stackId="a"
            fill="#8884d8"
            isAnimationActive={false}
          >
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={data.priorityColors[entry.priority] || "#ccc"}
              />
            ))}
          </Bar>
          {/* Due date markers as ReferenceLines */}
          {chartData.map((entry, index) => {
            const dueOffset =
              (entry.dueDate.getTime() -
                Math.min(...chartData.map((d) => d.startTimestamp))) /
              (1000 * 60 * 60 * 24)
            return (
              <ReferenceLine
                key={`due-${index}`}
                x={dueOffset}
                stroke="#FF0000"
                strokeDasharray="3 3"
              />
            )
          })}
          {/* Today marker */}
          {(() => {
            const todayOffset =
              (todayTimestamp -
                Math.min(...chartData.map((d) => d.startTimestamp))) /
              (1000 * 60 * 60 * 24)
            return (
              <ReferenceLine
                x={todayOffset}
                stroke="#000000"
                strokeDasharray="3 3"
                label={{ value: "Today", position: "top", fontSize: 12 }}
              />
            )
          })()}
        </BarChart>
      </ResponsiveContainer>

      {/* Bubble Chart */}
      <ResponsiveContainer width="99%" height={CHART_HEIGHT / 2}>
        <ScatterChart>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            type="number"
            dataKey="x"
            name="Days to Due"
            domain={["dataMin - 1", "dataMax + 1"]}
            label={{ value: "Days to Due", position: "top", fontSize: 12 }}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            type="number"
            dataKey="y"
            name="Amount"
            label={{
              value: "Amount ($)",
              angle: -90,
              position: "insideLeft",
              fontSize: 12,
            }}
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
          />
          <ZAxis type="number" dataKey="z" range={[50, 400]} name="Amount" />
          <Tooltip
            formatter={(value, name) => {
              if (name === "Days to Due") {
                return [`${value} days`, name]
              }
              if (name === "Amount") {
                return [`$${value.toLocaleString()}`, name]
              }
              return [value, name]
            }}
            labelFormatter={(label) => {
              const invoice = data.schedule.find(
                (item) => item.invoiceNumber === label
              )
              return `${invoice?.vendor}: ${label}`
            }}
            contentStyle={{ fontSize: "11px" }}
          />
          <Legend wrapperStyle={{ fontSize: 12, paddingTop: 8 }} />
          <Scatter
            name="Invoices"
            data={data.schedule.map((item) => {
              const dueDate = new Date(item.dueDate)
              const daysToDue = Math.round(
                (dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
              )

              return {
                x: daysToDue,
                y: item.amount,
                z: item.amount / 5000,
                name: item.invoiceNumber,
                vendor: item.vendor,
                category: item.category,
                priority: item.priority,
                dueDate: item.dueDate,
              }
            })}
            fill="#8884d8"
          >
            {data.schedule.map((item, index) => (
              <Cell
                key={`cell-${index}`}
                fill={data.priorityColors[item.priority]}
              />
            ))}
          </Scatter>
          <ReferenceLine
            x={0}
            stroke="#000"
            strokeDasharray="3 3"
            label={{ value: "Today", position: "top", fontSize: 12 }}
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  )
}

export default AccountsPayableChart
