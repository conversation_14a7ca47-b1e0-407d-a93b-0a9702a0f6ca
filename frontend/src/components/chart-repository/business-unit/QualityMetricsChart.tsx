import React from "react"
import {
  Legend,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

interface QualityMetricsChartProps {
  data: {
    qualityMetrics: Array<{
      unit: string
      preventiveScreening: number
      chronicDiseaseManagement: number
      readmissionRate: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const QualityMetricsChart: React.FC<QualityMetricsChartProps> = ({ data }) => {
  // Define the type for radar chart data
  interface RadarDataPoint {
    metric: string
    fullMark: number
    [key: string]: string | number // Index signature for unit names
  }

  // Transform data for radar chart
  const transformedData = [
    { metric: "Patient Volume", fullMark: 100 },
    { metric: "Revenue per Patient", fullMark: 100 },
    { metric: "Patient LTV", fullMark: 10 },
  ].map((item) => {
    const metricObj: RadarDataPoint = { ...item }

    data.qualityMetrics.forEach((unit) => {
      if (item.metric === "Patient Volume") {
        metricObj[unit.unit] = unit.preventiveScreening
      } else if (item.metric === "Revenue per Patient") {
        metricObj[unit.unit] = unit.chronicDiseaseManagement
      } else if (item.metric === "Patient LTV") {
        // Invert readmission rate (lower is better) to a 0-10 scale
        metricObj[unit.unit] = 10 - unit.readmissionRate
      }
    })

    return metricObj
  })

  // Map colors to units
  const getUnitColor = (unitName: string) => {
    const unit = data.businessUnits.find((u) => u.name === unitName)
    return unit ? unit.color : "#000000"
  }

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <RadarChart cx="50%" cy="65%" outerRadius="80%" data={transformedData}>
        <PolarGrid />
        <PolarAngleAxis dataKey="metric" tick={{ fontSize: 12 }} />
        <PolarRadiusAxis angle={30} domain={[0, 100]} />
        <Tooltip formatter={(value, name) => [`${value}%`, name]} />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        {data.qualityMetrics.map((unit) => (
          <Radar
            key={unit.unit}
            name={unit.unit}
            dataKey={unit.unit}
            stroke={getUnitColor(unit.unit)}
            fill={getUnitColor(unit.unit)}
            fillOpacity={0.3}
          />
        ))}
      </RadarChart>
    </ResponsiveContainer>
  )
}

export default QualityMetricsChart
