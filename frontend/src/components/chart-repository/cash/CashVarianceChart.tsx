import React from "react"
import {
  CartesianGrid,
  Cell,
  ReferenceLine,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON>hart,
  Tooltip,
  Treemap,
  XAxis,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON>xis,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface CashVarianceChartProps {
  data: {
    categories: string[]
    metrics: string[]
    data: {
      actual: number
      budget: number
      variance: number
    }[][]
    thresholds: {
      good: number
      warning: number
      danger: number
    }
    colors: {
      good: string
      warning: string
      danger: string
      neutral: string
    }
  }
}

const CustomTreemapContent = (props: any) => {
  const { root } = props

  const getBackgroundColor = (percentVariance: number): string => {
    const intensity = Math.min(Math.abs(percentVariance) / 10, 1)

    if (percentVariance >= 0) {
      // Positive variance (green with varying intensity)
      const r = Math.round(255 - intensity * 100)
      const g = 255
      const b = Math.round(255 - intensity * 100)
      return `rgba(${r}, ${g}, ${b}, 0.5)`
    } else {
      // Negative variance (red with varying intensity)
      const r = 255
      const g = Math.round(255 - intensity * 100)
      const b = Math.round(255 - intensity * 100)
      return `rgba(${r}, ${g}, ${b}, 0.5)`
    }
  }

  return (
    <g>
      {root.children?.map((node: any, i: number) => {
        const nodeItems = node.children || []
        return nodeItems.map((item: any, j: number) => {
          const itemX = item.x
          const itemY = item.y
          const itemWidth = item.width
          const itemHeight = item.height

          return (
            <g key={`${i}-${j}`}>
              <rect
                x={itemX}
                y={itemY}
                width={itemWidth}
                height={itemHeight}
                style={{
                  fill: getBackgroundColor(item.percentVariance),
                  stroke: "#fff",
                  strokeWidth: 2,
                  strokeOpacity: 1,
                }}
              />
              {itemWidth > 30 && itemHeight > 20 && (
                <text
                  x={itemX + itemWidth / 2}
                  y={itemY + itemHeight / 2}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  style={{
                    fontSize: 12,
                    fontWeight: "bold",
                    fill: "#333",
                  }}
                >
                  {item.name}
                </text>
              )}
            </g>
          )
        })
      })}
    </g>
  )
}

const CashVarianceChart: React.FC<CashVarianceChartProps> = ({ data }) => {
  // Process data for display
  const processedData = data.categories.flatMap((category, categoryIndex) => {
    return data.metrics.map((metric, metricIndex) => {
      const item = data.data[categoryIndex][metricIndex]
      return {
        category,
        metric,
        actual: item.actual,
        budget: item.budget,
        variance: item.actual - item.budget,
        percentVariance: item.variance,
      }
    })
  })

  // Helper function to determine cell color based on variance
  const getVarianceColor = (percentVariance: number): string => {
    if (percentVariance >= data.thresholds.good) {
      return data.colors.good
    } else if (percentVariance <= data.thresholds.danger) {
      return data.colors.danger
    } else if (percentVariance <= data.thresholds.warning) {
      return data.colors.warning
    } else {
      return data.colors.neutral
    }
  }

  // Helper function to get background color with intensity
  const getBackgroundColor = (percentVariance: number): string => {
    const intensity = Math.min(Math.abs(percentVariance) / 10, 1)

    if (percentVariance >= 0) {
      // Positive variance (green with varying intensity)
      const r = Math.round(255 - intensity * 100)
      const g = 255
      const b = Math.round(255 - intensity * 100)
      return `rgba(${r}, ${g}, ${b}, 1)`
    } else {
      // Negative variance (red with varying intensity)
      const r = 255
      const g = Math.round(255 - intensity * 100)
      const b = Math.round(255 - intensity * 100)
      return `rgba(${r}, ${g}, ${b}, 1)`
    }
  }

  // Transform data for treemap visualization - show clinics as main rectangles
  const treemapData = [
    {
      name: "Clinics",
      children: data.categories.map((category) => {
        const categoryItems = processedData.filter(
          (item) => item.category === category
        )
        // Calculate total variance for the clinic (sum of absolute variances)
        const totalVariance = categoryItems.reduce(
          (sum, item) => sum + Math.abs(item.variance),
          0
        )
        // Calculate average percentage variance for color coding
        const avgPercentVariance =
          categoryItems.reduce((sum, item) => sum + item.percentVariance, 0) /
          categoryItems.length

        return {
          name: category,
          size: totalVariance,
          percentVariance: avgPercentVariance,
          metrics: categoryItems,
          color: getVarianceColor(avgPercentVariance),
        }
      }),
    },
  ]

  // Create scatter plot data for variance analysis
  const scatterData = processedData.map((item) => ({
    x: item.budget, // Budget on x-axis
    y: item.actual, // Actual on y-axis
    z: Math.abs(item.percentVariance), // Size based on variance percentage
    name: `${item.category}: ${item.metric}`,
    category: item.category,
    metric: item.metric,
    percentVariance: item.percentVariance,
    variance: item.variance,
  }))

  // Custom tooltip for treemap
  const CustomTreemapTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <p className="font-semibold">{data.name}</p>
          <p>
            Average Variance: {data.percentVariance >= 0 ? "+" : ""}
            {data.percentVariance.toFixed(1)}%
          </p>
          <p className="mt-1 text-xs text-gray-600">Metrics breakdown:</p>
          {data.metrics?.map((metric: any, index: number) => (
            <div key={index} className="text-xs">
              <span className="font-medium">{metric.metric}:</span>
              <span style={{ color: getVarianceColor(metric.percentVariance) }}>
                {" "}
                {formatAbbreviatedCurrency(metric.variance, 0)} (
                {metric.percentVariance >= 0 ? "+" : ""}
                {metric.percentVariance.toFixed(1)}%)
              </span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  // Custom tooltip for scatter plot
  const CustomScatterTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <p className="font-semibold">{data.name}</p>
          <p>Actual: {formatAbbreviatedCurrency(data.y, 0)}</p>
          <p>Budget: {formatAbbreviatedCurrency(data.x, 0)}</p>
          <p style={{ color: getVarianceColor(data.percentVariance) }}>
            Variance: {formatAbbreviatedCurrency(data.variance, 0)}(
            {data.percentVariance >= 0 ? "+" : ""}
            {data.percentVariance.toFixed(1)}%)
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="flex flex-col">
      <div className="grid flex-1 grid-cols-1 gap-2 md:grid-cols-2">
        {/* Treemap visualization */}
        <div>
          <h4 className="mb-2 text-center text-xs font-medium">
            Variance by Clinic & Metric
          </h4>
          <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
            <Treemap
              data={treemapData}
              dataKey="size"
              aspectRatio={4 / 3}
              animationDuration={500}
              content={<CustomTreemapContent />}
            >
              <Tooltip content={<CustomTreemapTooltip />} />
            </Treemap>
          </ResponsiveContainer>
        </div>

        {/* Scatter plot for budget vs actual */}
        <div>
          <h4 className="mb-2 text-center text-xs font-medium">
            Budget vs. Actual
          </h4>
          <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
            <ScatterChart margin={{ bottom: 20, left: 20 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                type="number"
                dataKey="x"
                name="Budget"
                label={{
                  value: "Budget",
                  position: "bottom",
                  offset: 0,
                  fontSize: 12,
                }}
                tickFormatter={(value) =>
                  `$${formatAbbreviatedCurrency(value, 0)}`
                }
                fontSize={12}
              />
              <YAxis
                type="number"
                dataKey="y"
                name="Actual"
                label={{
                  value: "Actual",
                  angle: -90,
                  position: "left",
                  offset: 0,
                  fontSize: 12,
                }}
                tickFormatter={(value) =>
                  `$${formatAbbreviatedCurrency(value, 0)}`
                }
                fontSize={12}
              />
              <ZAxis
                type="number"
                dataKey="z"
                range={[50, 400]}
                name="Variance %"
              />
              <Tooltip content={<CustomScatterTooltip />} />
              <ReferenceLine x={0} stroke="#666" />
              <ReferenceLine y={0} stroke="#666" />
              <ReferenceLine y="x" stroke="#666" strokeDasharray="3 3" />
              <Scatter
                name="Budget vs Actual"
                data={scatterData}
                fill="#8884d8"
              >
                {scatterData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={getBackgroundColor(entry.percentVariance)}
                  />
                ))}
              </Scatter>
            </ScatterChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="mt-4 flex items-center justify-center text-xs text-gray-500">
        <div className="mr-4 flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: data.colors.good }}
          ></div>
          <span>Favorable ≥ {data.thresholds.good}%</span>
        </div>
        <div className="mr-4 flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: data.colors.neutral }}
          ></div>
          <span>Neutral</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: data.colors.danger }}
          ></div>
          <span>Unfavorable ≤ {data.thresholds.danger}%</span>
        </div>
      </div>
    </div>
  )
}

export default CashVarianceChart
