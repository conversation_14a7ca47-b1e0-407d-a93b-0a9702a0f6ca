"use client"

import React from "react"
import {
  Bar,
  CartesianGrid,
  Cell,
  ComposedChart,
  Line,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"
import { MonthlyRevenueData } from "@/components/chart-repository/data/monthlyRevenueData"

const MonthlyRevenueWithSMAChart = ({
  data,
}: {
  data: MonthlyRevenueData[]
}) => {
  // Format data to show month and year on x-axis in MMM-YY format
  const formattedData = data.map((item) => ({
    ...item,
    monthYear: `${item.month}-${item.year.toString().substring(2)}`,
    // Add a year property for color coding
    yearForColor: item.year,
  }))

  // Create a separate dataset for the bottom chart to avoid legend issues
  const bottomChartData = data.map((item) => ({
    monthYear: `${item.month}-${item.year.toString().substring(2)}`,
    momChange: item.changeVsPrevMonth,
  }))

  // Year-based colors for the bars
  const yearColors = {
    2023: "#90CAF9", // Light blue for 2023
    2024: "#2196F3", // Blue for 2024
    2025: "#0D47A1", // Dark blue for 2025
  }

  const formatYAxis = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`
    }
    return `$${value}`
  }

  // Custom tooltip to show all values in a consolidated format
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Find the data for the current month
      let revenueValue = null
      let sma3Value = null
      let sma6Value = null
      let momChangeValue = null

      // Find the corresponding data point in the full dataset
      const currentDataPoint = data.find((item) => {
        const monthYear = `${item.month}-${item.year.toString().substring(2)}`
        return monthYear === label
      })

      // Extract all values from payload
      payload.forEach((entry: any) => {
        if (entry.name === "Revenue") revenueValue = entry.value
        if (entry.name === "3-Month SMA") sma3Value = entry.value
        if (entry.name === "6-Month SMA") sma6Value = entry.value
        // Handle MoM Change % differently to avoid it showing in the top chart
        if (entry.dataKey === "momChange") momChangeValue = entry.value
      })

      // If momChangeValue is not found in payload, get it from the dataset
      if (momChangeValue === null && currentDataPoint) {
        momChangeValue = currentDataPoint.changeVsPrevMonth
      }

      // Also check the bottomChartData for the MoM change value
      if (momChangeValue === null) {
        const bottomDataPoint = bottomChartData.find(
          (item) => item.monthYear === label
        )
        if (bottomDataPoint) {
          momChangeValue = bottomDataPoint.momChange
        }
      }

      return (
        <div className="rounded border border-gray-300 bg-white p-3 shadow-lg">
          <p className="mb-2 text-lg font-bold text-gray-900">{label}</p>
          <div className="space-y-1">
            {revenueValue !== null && (
              <p className="font-medium text-gray-800">
                Revenue:{" "}
                <span className="font-bold">
                  ${(revenueValue / 1000000).toFixed(2)}M
                </span>
              </p>
            )}
            {sma3Value !== null && (
              <p className="text-gray-800">
                3-Month SMA:{" "}
                <span className="font-bold">
                  ${(sma3Value / 1000000).toFixed(2)}M
                </span>
              </p>
            )}
            {sma6Value !== null && (
              <p className="text-gray-800">
                6-Month SMA:{" "}
                <span className="font-bold">
                  ${(sma6Value / 1000000).toFixed(2)}M
                </span>
              </p>
            )}
            {momChangeValue !== null && (
              <p
                className={`font-medium ${momChangeValue >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                MoM Change:{" "}
                <span className="font-bold">
                  {typeof momChangeValue === "number"
                    ? momChangeValue.toFixed(2)
                    : 0}
                  %
                </span>
              </p>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  const shouldShowLabel = (index: number): boolean => index % 3 === 0

  return (
    <div className="flex flex-col">
      <div className="my-1 flex justify-center space-x-6">
        <div className="flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: yearColors[2023] }}
          ></div>
          <span className="text-xs font-medium text-gray-900">2023</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: yearColors[2024] }}
          ></div>
          <span className="text-xs font-medium text-gray-900">2024</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: yearColors[2025] }}
          ></div>
          <span className="text-xs font-medium text-gray-900">2025</span>
        </div>
      </div>

      <div className="mb-1 flex justify-center space-x-4">
        <div className="flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: "#2196F3" }}
          ></div>
          <span className="text-xs font-medium text-gray-900">Revenue</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 h-1 w-6"
            style={{ backgroundColor: "#444444" }}
          ></div>
          <span className="text-xs font-medium text-gray-900">3-Month SMA</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 h-1 w-6"
            style={{ backgroundColor: "#424242" }}
          ></div>
          <span className="text-xs font-medium text-gray-900">6-Month SMA</span>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT / 2}>
        <ComposedChart data={formattedData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="monthYear"
            hide={true} // Hide x-axis labels on top chart
            tick={{ fill: "#000000" }}
          />
          <YAxis
            tickFormatter={formatYAxis}
            domain={[
              (dataMin: number) => dataMin * 0.8,
              (dataMax: number) => dataMax * 1.2,
            ]}
            tick={{ fill: "#000000", fontSize: 12 }}
            width={35}
          />
          <Tooltip content={<CustomTooltip />} />
          {/* No legend in top chart to avoid MoM Change % label issues */}
          <Bar dataKey="revenue" name="Revenue">
            {formattedData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={yearColors[entry.yearForColor as keyof typeof yearColors]}
              />
            ))}
          </Bar>
          <Line
            dataKey="sma3"
            stroke="#444444" // Darker grey for 3-month SMA
            strokeWidth={2}
            dot={false}
            name="3-Month SMA"
          />
          <Line
            dataKey="sma6"
            stroke="#424242" // Dark grey for 6-month SMA
            strokeWidth={2}
            dot={false}
            name="6-Month SMA"
          />
          {/* No hidden bar needed */}
        </ComposedChart>
      </ResponsiveContainer>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT / 2}>
        <ComposedChart data={bottomChartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="monthYear"
            angle={-45}
            textAnchor="end"
            height={40}
            interval={0}
            // Use a custom tick formatter to show only every 3rd month
            tickFormatter={(value, index) => {
              return shouldShowLabel(index) ? value : ""
            }}
            tick={{ fontSize: 12, fill: "#000000" }}
          />
          <YAxis
            tickFormatter={(value) => `${value}%`}
            domain={[-15, 15]}
            ticks={[-15, -10, -5, 0, 5, 10, 15]}
            tick={{ fill: "#000000", fontSize: 12 }}
            width={35}
          />
          <Tooltip content={<CustomTooltip />} />
          {/* No legend in bottom chart - we use the title above instead */}
          <ReferenceLine y={0} stroke="#000" strokeWidth={1} />
          {/* Custom Bar component without a name attribute to prevent MoM Change % from showing in legends */}
          <Bar dataKey="momChange" isAnimationActive={false}>
            {bottomChartData.map((entry, index) => (
              <Cell
                key={`mom-cell-${index}`}
                fill={(entry.momChange ?? 0) >= 0 ? "#4CAF50" : "#F44336"}
              />
            ))}
          </Bar>
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  )
}

export default MonthlyRevenueWithSMAChart
