"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  LabelList,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"
import { SegmentPerformanceData } from "@/components/chart-repository/data/segmentPerformanceData"

const SegmentPerformanceChart = ({
  data,
}: {
  data: SegmentPerformanceData
}) => {
  // Format data for the chart
  const formattedData = data.segments.map((segment) => ({
    name: segment.name,
    previousYear: segment.previousYearRevenue,
    currentYear: segment.currentYearRevenue,
    percentChange: segment.percentageChange,
    // Format for display
    previousYearLabel: `${data.year - 1}`,
    currentYearLabel: `${data.year}`,
  }))

  const formatYAxis = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`
    }
    return `$${value}`
  }

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const currentYear = data.year
      const previousYear = data.year - 1

      return (
        <div className="rounded border border-gray-300 bg-white p-3 shadow-lg">
          <p className="mb-2 text-lg font-bold text-gray-900">{label}</p>
          <div className="space-y-1">
            <p className="font-medium text-gray-800">
              {previousYear}:{" "}
              <span className="font-bold">
                ${(payload[0]?.value / 1000000).toFixed(2)}M
              </span>
            </p>
            <p className="font-medium text-gray-800">
              {currentYear}:{" "}
              <span className="font-bold">
                ${(payload[1]?.value / 1000000).toFixed(2)}M
              </span>
            </p>
            <p
              className={`font-medium ${payload[1]?.value - payload[0]?.value >= 0 ? "text-green-600" : "text-red-600"}`}
            >
              Change:{" "}
              <span className="font-bold">
                {(
                  ((payload[1]?.value - payload[0]?.value) /
                    payload[0]?.value) *
                  100
                ).toFixed(1)}
                %
              </span>
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  // Custom label for percentage change
  const renderCustomizedLabel = (props: any) => {
    const { x, y, width, index } = props
    const item = formattedData[index]
    const percentChange = item.percentChange
    const isPositive = percentChange >= 0

    // Position the bubble in the middle between the bars
    const bubbleX = x + width / 2
    const bubbleY = y - 20 // Position above the bars

    return (
      <g>
        <circle
          cx={bubbleX}
          cy={bubbleY}
          r={16}
          fill={isPositive ? "#4CAF50" : "#F44336"}
        />
        <text
          x={bubbleX}
          y={bubbleY}
          textAnchor="middle"
          dominantBaseline="middle"
          fill="white"
          fontSize="10"
          fontWeight="bold"
        >
          {isPositive ? "+" : ""}
          {percentChange.toFixed(1)}%
        </text>
      </g>
    )
  }

  return (
    <div className="flex flex-col">
      {/* Legend */}
      <div className="mt-2 flex justify-center space-x-6">
        <div className="flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: "#90CAF9" }}
          ></div>
          <span className="text-xs font-medium text-gray-900">
            {data.year - 1}
          </span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 h-3 w-3"
            style={{ backgroundColor: "#0D47A1" }}
          ></div>
          <span className="text-xs font-medium text-gray-900">{data.year}</span>
        </div>
      </div>

      {/* Chart */}
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <BarChart
          data={formattedData}
          margin={{
            top: 30, // Increased top margin for the percentage bubbles
            right: 30,
            left: 40,
            bottom: 20,
          }}
          barGap={0} // No gap between bars in the same category
          barCategoryGap={30} // Gap between different categories
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" tick={{ fontSize: 12, fill: "#000000" }} />
          <YAxis
            tickFormatter={formatYAxis}
            domain={[0, (dataMax: number) => dataMax * 1.2]}
            tick={{ fontSize: 12, fill: "#000000" }}
            width={40}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="previousYear"
            name={`${data.year - 1}`}
            fill="#90CAF9"
          />
          <Bar dataKey="currentYear" name={`${data.year}`} fill="#0D47A1">
            <LabelList
              dataKey="percentChange"
              position="top"
              content={renderCustomizedLabel}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

export default SegmentPerformanceChart
