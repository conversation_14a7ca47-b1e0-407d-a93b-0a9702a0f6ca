import React from "react"
import {
  Cell,
  Line,
  <PERSON>hart,
  <PERSON>,
  <PERSON><PERSON>,
  ReferenceLine,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

interface CashConversionMetricsChartProps {
  data: {
    metrics: {
      name: string
      current: number
      target: number
      history: number[]
      months: string[]
      description: string
      goodDirection: string
    }[]
    zones: {
      danger: string
      warning: string
      target: string
    }
  }
}

interface TrendDataPoint {
  month: string
  value: number
}

interface GaugeDataPoint {
  name: string
  value: number
}

const CashConversionMetricsChart: React.FC<CashConversionMetricsChartProps> = ({
  data,
}) => {
  // Helper function to determine color based on current value vs target
  const getMetricColor = (metric: (typeof data.metrics)[0]) => {
    const diff = Math.abs(metric.current - metric.target) / metric.target

    if (metric.goodDirection === "down") {
      if (metric.current <= metric.target) {
        return data.zones.target
      } else if (diff < 0.2) {
        return data.zones.warning
      } else {
        return data.zones.danger
      }
    } else {
      if (metric.current >= metric.target) {
        return data.zones.target
      } else if (diff < 0.2) {
        return data.zones.warning
      } else {
        return data.zones.danger
      }
    }
  }

  // Transform history data for LineChart
  const transformTrendData = (
    metric: (typeof data.metrics)[0]
  ): TrendDataPoint[] => {
    return metric.months.map((month, index) => ({
      month,
      value: metric.history[index],
    }))
  }

  // Create gauge data for PieChart
  const createGaugeData = (
    metric: (typeof data.metrics)[0]
  ): { gaugeData: GaugeDataPoint[]; targetAngle: number } => {
    // Determine min and max values for the gauge
    let minValue, maxValue
    if (metric.goodDirection === "down") {
      minValue = 0
      maxValue = Math.max(metric.current, metric.target) * 1.5
    } else {
      minValue = Math.min(metric.current, metric.target) * 0.5
      maxValue = Math.max(metric.current, metric.target) * 1.5
    }

    // Calculate percentage for the gauge
    const percentage = (metric.current - minValue) / (maxValue - minValue)
    const clampedPercentage = Math.min(Math.max(percentage, 0), 1)

    // Calculate target percentage for the gauge
    const targetPercentage = (metric.target - minValue) / (maxValue - minValue)
    const clampedTargetPercentage = Math.min(Math.max(targetPercentage, 0), 1)

    // Angle for the target line (180 degrees for half circle, 180 is start, 0 is end)
    // The angle needs to be mapped from 0-1 to 180-0
    const targetAngle = 180 - clampedTargetPercentage * 180

    return {
      gaugeData: [
        { name: "value", value: clampedPercentage * 0.5 }, // Only show half circle (0.5)
        { name: "empty", value: 0.5 - clampedPercentage * 0.5 },
      ],
      targetAngle,
    }
  }

  // Custom tooltip for trend line
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded border border-gray-200 bg-white p-2 text-xs shadow-md">
          <p className="font-semibold">{`Month: ${label}`}</p>
          <p>{`Value: ${payload[0].value}`}</p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-full w-full">
      <div className="grid grid-cols-2 gap-4">
        {data.metrics.map((metric, i) => {
          const color = getMetricColor(metric)
          const trendData = transformTrendData(metric)
          const { gaugeData, targetAngle } = createGaugeData(metric)

          return (
            <div key={i} className="flex flex-col items-center">
              {/* Gauge Chart */}
              <div className="relative flex h-40 w-full items-center justify-center">
                <div className="absolute text-center" style={{ top: "30%" }}>
                  {/* Adjusted top position further up */}
                  <div className="text-2xl font-bold">{metric.current}</div>
                  <div className="mt-4 text-xs">{metric.name}</div>
                </div>
                <ResponsiveContainer width="99%" height="100%">
                  <PieChart>
                    <Pie
                      data={gaugeData}
                      cx="50%"
                      cy="50%"
                      startAngle={180}
                      endAngle={0}
                      innerRadius="60%"
                      outerRadius="80%"
                      paddingAngle={0}
                      dataKey="value"
                      stroke="none"
                    >
                      <Cell key="cell-0" fill={color} />
                      <Cell key="cell-1" fill="#E2E8F0" />
                    </Pie>
                    {/* Target marker - now a thin line */}
                    <Pie
                      data={[{ name: "target", value: 1 }]} // A dummy value, the angle defines the position
                      cx="50%"
                      cy="50%"
                      startAngle={targetAngle - 0.5} // Small arc to simulate a line
                      endAngle={targetAngle + 0.5}
                      innerRadius="60%" // Match inner radius of main gauge
                      outerRadius="80%" // Match outer radius of main gauge
                      paddingAngle={0}
                      dataKey="value"
                      stroke="none"
                    >
                      <Cell key="marker" fill="#000" />
                    </Pie>
                    <text
                      x="50%"
                      y="53%" // Adjusted y position to prevent overlap
                      textAnchor="middle"
                      dominantBaseline="middle"
                      className="fill-current text-xs text-gray-700"
                    >
                      Target: {metric.target}
                    </text>
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Trend Line Chart */}
              <div className="-mt-10 h-20 w-full">
                <ResponsiveContainer width="99%" height="100%">
                  <LineChart data={trendData}>
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 8 }}
                      interval={Math.floor(metric.months.length / 4)}
                    />
                    <YAxis hide />
                    <Tooltip content={<CustomTooltip />} />
                    <ReferenceLine
                      y={metric.target}
                      stroke="#000"
                      strokeDasharray="3 3"
                    />
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke={color}
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default CashConversionMetricsChart
