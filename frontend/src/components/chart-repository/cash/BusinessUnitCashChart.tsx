import React from "react"
import {
  Tooltip as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  Tree<PERSON>p,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface BusinessUnitCashChartProps {
  data: {
    cashGeneration: {
      name: string
      value: number
      color: string
      percentChange: number
    }[]
    cashUsage: {
      name: string
      value: number
      color: string
    }[]
  }
}

const BusinessUnitCashChart: React.FC<BusinessUnitCashChartProps> = ({
  data,
}) => {
  // Format percentage
  const formatPercent = (value: number) => {
    return `${value >= 0 ? "+" : ""}${value}%`
  }

  // Helper function to get background color with intensity based on percentChange
  const getBackgroundColor = (percentChange: number): string => {
    // Use green for positive, red for negative, with intensity
    const intensity = Math.min(Math.abs(percentChange) / 10, 1)
    if (percentChange >= 0) {
      // Positive (green)
      const r = Math.round(255 - intensity * 100)
      const g = 255
      const b = Math.round(255 - intensity * 100)
      return `rgba(${r}, ${g}, ${b}, 0.7)`
    } else {
      // Negative (red)
      const r = 255
      const g = Math.round(255 - intensity * 100)
      const b = Math.round(255 - intensity * 100)
      return `rgba(${r}, ${g}, ${b}, 0.7)`
    }
  }

  // Custom tooltip for treemap
  const TreemapTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <div className="font-semibold">{data.name}</div>
          <div>Cash Flow: {formatAbbreviatedCurrency(data.value, 0)}</div>
          <div>Change: {formatPercent(data.percentChange)}</div>
        </div>
      )
    }
    return null
  }

  // Custom treemap content with improved styling
  const TreemapContent = (props: any) => {
    const { root } = props
    if (!root || !root.children) return null
    return (
      <g>
        {root.children.map((node: any, i: number) => {
          const { x, y, width, height, name, value, percentChange } = node
          // Only render text if rectangle is large enough
          const showText = width > 40 && height > 30
          return (
            <g key={i}>
              <rect
                x={x}
                y={y}
                width={width}
                height={height}
                style={{
                  fill: getBackgroundColor(percentChange),
                  stroke: "#fff",
                  strokeWidth: 2,
                  strokeOpacity: 1,
                }}
              />
              {showText && (
                <>
                  <text
                    x={x + width / 2}
                    y={y + height / 2 - 6}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    style={{ fontSize: 12, fontWeight: "bold", fill: "#333" }}
                  >
                    {name}
                  </text>
                  <text
                    x={x + width / 2}
                    y={y + height / 2 + 10}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    style={{ fontSize: 12, fill: "#333" }}
                  >
                    {formatAbbreviatedCurrency(value, 0)}
                  </text>
                </>
              )}
            </g>
          )
        })}
      </g>
    )
  }

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <Treemap
        data={data.cashGeneration}
        dataKey="value"
        nameKey="name"
        content={<TreemapContent />}
      >
        <RechartsTooltip content={<TreemapTooltip />} />
      </Treemap>
    </ResponsiveContainer>
  )
}

export default BusinessUnitCashChart
