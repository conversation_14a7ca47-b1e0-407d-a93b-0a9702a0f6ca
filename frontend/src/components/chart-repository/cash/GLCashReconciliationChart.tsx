"use client"

import React from "react"
import {
  Bar,
  CartesianGrid,
  Cell,
  ComposedChart,
  LabelList,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface WaterfallChartItem {
  name: string
  base: number
  newBase: number
  deltaPositive: number
  deltaNegative: number
  actualValue: number
  fill: string
  isTotal: boolean
}

interface GLCashReconciliationChartProps {
  data: {
    openingBalance: number
    closingBalance: number
    movements: {
      name: string
      value: number
      type: string
    }[]
    colors: {
      [key: string]: string
    }
  }
}

const GLCashReconciliationChart: React.FC<GLCashReconciliationChartProps> = ({
  data,
}) => {
  const formatWaterfallData = () => {
    const result: WaterfallChartItem[] = []
    let runningTotal = data.openingBalance

    // Opening Balance
    result.push({
      name: "Opening Balance",
      base: 0,
      newBase: data.opening<PERSON><PERSON>ce,
      deltaPositive: data.openingBalance,
      deltaNegative: 0,
      actualValue: data.openingBalance,
      fill: data.colors.balance || "#1f77b4",
      isTotal: true,
    })

    // Movements
    data.movements
      .filter(
        (movement) =>
          movement.name !== "Opening Balance" &&
          movement.name !== "Closing Balance"
      )
      .forEach((movement) => {
        const isPositive = movement.value >= 0

        result.push({
          name: movement.name,
          base: isPositive ? runningTotal : runningTotal + movement.value, // Adjust base for negative values
          newBase: runningTotal + movement.value,
          deltaPositive: isPositive ? movement.value : 0,
          deltaNegative: isPositive ? 0 : Math.abs(movement.value),
          actualValue: movement.value,
          fill: isPositive
            ? data.colors.positive || "#2ca02c"
            : data.colors.negative || "#d62728",
          isTotal: false,
        })

        runningTotal += movement.value
      })

    // Closing Balance
    result.push({
      name: "Closing Balance",
      base: 0,
      newBase: runningTotal, // Use the final runningTotal for closing balance
      deltaPositive: runningTotal,
      deltaNegative: 0,
      actualValue: runningTotal,
      fill: data.colors.balance || "#1f77b4",
      isTotal: true,
    })

    return result
  }

  const waterfallData = formatWaterfallData()

  const formatLabelValue = (value: number) => {
    const sign = value > 0 ? "+" : ""
    return `${sign}${formatAbbreviatedCurrency(value)}`
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="rounded-md border border-gray-300 bg-white p-2 shadow-md">
          <p className="font-semibold">{data.name}</p>
          <p className="text-sm">
            Value: {formatAbbreviatedCurrency(data.actualValue)}
          </p>
          {!data.isTotal && (
            <>
              <p className="text-sm">
                Previous: {formatAbbreviatedCurrency(data.base)}
              </p>
              <p className="text-sm">
                New Total: {formatAbbreviatedCurrency(data.newBase)}
              </p>
            </>
          )}
        </div>
      )
    }
    return null
  }

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT * 1.25}>
      <ComposedChart data={waterfallData} margin={{ top: 24, bottom: 48 }}>
        <CartesianGrid strokeDasharray="3 3" vertical={false} />
        <XAxis
          dataKey="name"
          angle={-45}
          textAnchor="end"
          height={60}
          interval={0}
          tick={{ fontSize: 12 }}
        />
        <YAxis
          tickFormatter={formatAbbreviatedCurrency}
          axisLine={false}
          tickLine={false}
          domain={["dataMin", "dataMax"]}
          tick={{ fontSize: 12, fill: "#000000" }}
          width={55}
        />
        <Tooltip content={<CustomTooltip />} />

        <Bar dataKey="base" stackId="stack" isAnimationActive={false}>
          {waterfallData.map((entry, index) => (
            <Cell key={`cell-base-${index}`} fill="transparent" />
          ))}
        </Bar>

        <Bar dataKey="deltaPositive" stackId="stack" name="Positive">
          {waterfallData.map((entry, index) => (
            <Cell
              key={`cell-pos-${index}`}
              fill={entry.deltaPositive > 0 ? entry.fill : "transparent"}
            />
          ))}
        </Bar>

        <Bar dataKey="deltaNegative" stackId="stack" name="Negative">
          {waterfallData.map((entry, index) => (
            <Cell
              key={`cell-neg-${index}`}
              fill={entry.deltaNegative > 0 ? "#F44336" : "transparent"}
            />
          ))}
          <LabelList
            dataKey="actualValue"
            position="top"
            formatter={(value: any) => {
              if (value === 0) return ""
              return formatLabelValue(value)
            }}
            fill="#000000"
            fontSize={12}
          />
        </Bar>
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default GLCashReconciliationChart
