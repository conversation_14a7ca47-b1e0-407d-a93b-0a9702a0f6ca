import React from "react"
import {
  <PERSON>,
  CartesianG<PERSON>,
  Composed<PERSON>hart,
  Legend,
  Line,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface CashForecastChartProps {
  data: {
    weeks: string[]
    segments: {
      name: string
      color: string
      data: number[]
    }[]
    actual: {
      name: string
      segments: {
        name: string
        data: (number | null)[]
      }[]
    }
  }
}

interface ChartDataPoint {
  week: string
  [key: string]: string | number | null
}

const CashForecastChart: React.FC<CashForecastChartProps> = ({ data }) => {
  // Transform data for Recharts
  const transformData = (): ChartDataPoint[] => {
    return data.weeks.map((week, index) => {
      const result: ChartDataPoint = { week }

      // Add forecast data for each segment
      data.segments.forEach((segment) => {
        result[`forecast_${segment.name}`] = segment.data[index]
      })

      // Add actual data for each segment (if available)
      data.actual.segments.forEach((segment) => {
        const actualValue = segment.data[index]
        if (actualValue !== null) {
          result[`actual_${segment.name}`] = actualValue
        }
      })

      // Calculate total forecast for this week
      const forecastTotal = data.segments.reduce(
        (sum, segment) => sum + segment.data[index],
        0
      )
      result.forecastTotal = forecastTotal

      // Calculate total actual for this week (only if all segments have actual data)
      const actualTotal = data.actual.segments.reduce((sum, segment) => {
        const value = segment.data[index]
        return value !== null ? sum + value : sum
      }, 0)

      // Only add actual total if we have actual data for this week
      const hasActualData = data.actual.segments.some(
        (segment) => segment.data[index] !== null
      )
      if (hasActualData) {
        result.actualTotal = actualTotal
      }

      return result
    })
  }

  const chartData = transformData()

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Get total lines
      const forecastTotalEntry = payload.find(
        (entry: any) => entry.dataKey === "forecastTotal"
      )
      const actualTotalEntry = payload.find(
        (entry: any) => entry.dataKey === "actualTotal"
      )

      // Group segments by name, combining forecast and actual
      const segmentMap = new Map()

      payload.forEach((entry: any) => {
        if (entry.dataKey.startsWith("forecast_")) {
          const segmentName = entry.dataKey.replace("forecast_", "")
          if (!segmentMap.has(segmentName)) {
            segmentMap.set(segmentName, {
              name: segmentName,
              color: entry.color,
            })
          }
          segmentMap.get(segmentName).forecast = entry.value
        } else if (entry.dataKey.startsWith("actual_")) {
          const segmentName = entry.dataKey.replace("actual_", "")
          if (!segmentMap.has(segmentName)) {
            segmentMap.set(segmentName, {
              name: segmentName,
              color: entry.color,
            })
          }
          segmentMap.get(segmentName).actual = entry.value
        }
      })

      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <p className="font-semibold">Week: {label}</p>

          {/* Show total lines */}
          {forecastTotalEntry && (
            <p className="mt-2 font-medium" style={{ color: "#2F3E9E" }}>
              Total Forecast:{" "}
              {formatAbbreviatedCurrency(forecastTotalEntry.value as number, 0)}
            </p>
          )}
          {actualTotalEntry && (
            <p className="font-medium" style={{ color: "#ED8936" }}>
              Total Actual:{" "}
              {formatAbbreviatedCurrency(actualTotalEntry.value as number, 0)}
            </p>
          )}

          {/* Show segments with both forecast and actual */}
          {segmentMap.size > 0 && (
            <>
              <p className="mt-2 font-medium text-gray-700">Segments:</p>
              {Array.from(segmentMap.values()).map(
                (segment: any, index: number) => (
                  <div
                    key={`segment-${index}`}
                    style={{ color: segment.color }}
                  >
                    <span className="font-medium">{segment.name}:</span>
                    {segment.forecast && (
                      <span className="ml-1">
                        F: {formatAbbreviatedCurrency(segment.forecast, 0)}
                      </span>
                    )}
                    {segment.actual && (
                      <span className="ml-2">
                        A: {formatAbbreviatedCurrency(segment.actual, 0)}
                      </span>
                    )}
                  </div>
                )
              )}
            </>
          )}
        </div>
      )
    }
    return null
  }

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" vertical={false} />
        <XAxis
          dataKey="week"
          angle={-45}
          textAnchor="end"
          height={60}
          tick={{ fontSize: 12 }}
        />
        <YAxis
          tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
          tick={{ fontSize: 12 }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 8 }}
        />

        {/* Forecast stacked bars */}
        {data.segments.map((segment) => (
          <Bar
            key={`forecast-${segment.name}`}
            dataKey={`forecast_${segment.name}`}
            stackId="forecast"
            fill={segment.color}
            name={segment.name}
            fillOpacity={0.7}
            legendType="none"
          />
        ))}

        {/* Actual stacked bars - only show in legend */}
        {data.segments.map((segment) => (
          <Bar
            key={`actual-${segment.name}`}
            dataKey={`actual_${segment.name}`}
            stackId="actual"
            fill={segment.color}
            name={segment.name}
            fillOpacity={1}
          />
        ))}

        {/* Forecast total line */}
        <Line
          type="monotone"
          dataKey="forecastTotal"
          name="Total Forecast"
          stroke="#2F3E9E"
          strokeWidth={3}
          strokeDasharray="5 5"
          dot={{ r: 4, fill: "#2F3E9E" }}
          activeDot={{ r: 6 }}
          isAnimationActive={false}
        />

        {/* Actual total line */}
        <Line
          type="monotone"
          dataKey="actualTotal"
          name="Total Actual"
          stroke="#ED8936"
          strokeWidth={3}
          dot={{ r: 4, fill: "#ED8936" }}
          activeDot={{ r: 6 }}
          connectNulls={false}
          isAnimationActive={false}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default CashForecastChart
