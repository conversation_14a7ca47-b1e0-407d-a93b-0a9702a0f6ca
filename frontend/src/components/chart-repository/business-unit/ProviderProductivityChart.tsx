import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface ProviderProductivityChartProps {
  data: {
    providerProductivity: Array<{
      unit: string
      patientsPerDay: number
      revenuePerPatient: number
      benchmark: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const ProviderProductivityChart: React.FC<ProviderProductivityChartProps> = ({
  data,
}) => (
  <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
    <BarChart data={data.providerProductivity} barGap={0} barCategoryGap="20%">
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="unit" tick={{ fontSize: 12 }} />
      <YAxis
        yAxisId="left"
        label={{
          value: "Patients Per Day",
          angle: -90,
          position: "insideLeft",
          fontSize: 12,
        }}
        tick={{ fontSize: 12 }}
      />
      <YAxis
        yAxisId="right"
        orientation="right"
        label={{
          value: "Revenue per Patient ($)",
          angle: 90,
          position: "insideRight",
          fontSize: 12,
        }}
        tick={{ fontSize: 12 }}
        tickFormatter={(value) => `$${formatAbbreviatedCurrency(value, 0)}`}
      />
      <Tooltip
        formatter={(value, name) => {
          if (name === "Revenue per Patient")
            return [`$${formatAbbreviatedCurrency(value as number, 0)}`, name]
          return [value, name]
        }}
      />
      <Legend
        verticalAlign="top"
        wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
      />
      <Bar
        yAxisId="left"
        dataKey="patientsPerDay"
        name="Patients Per Day"
        fill="#8884d8"
      />
      <Bar
        yAxisId="right"
        dataKey="revenuePerPatient"
        name="Revenue per Patient"
        fill="#82ca9d"
      />
      <ReferenceLine
        yAxisId="left"
        stroke="#ff7300"
        strokeWidth={2}
        strokeDasharray="3 3"
        segment={data.providerProductivity.map((entry, index) => ({
          x: index,
          y: entry.benchmark,
        }))}
        label={{
          value: "Benchmark",
          position: "insideTopRight",
          fontSize: 12,
        }}
      />
    </BarChart>
  </ResponsiveContainer>
)

export default ProviderProductivityChart
