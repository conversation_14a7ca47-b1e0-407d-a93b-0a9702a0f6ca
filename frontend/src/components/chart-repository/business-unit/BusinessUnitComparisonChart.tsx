import React from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface BusinessUnitComparisonChartProps {
  data: {
    profitability: Array<{
      unit: string
      revenue: number
      expenses: number
      profit: number
      margin: number
    }>
    patientSatisfaction: Array<{
      unit: string
      score: number
      benchmark: number
      responses: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const BusinessUnitComparisonChart: React.FC<
  BusinessUnitComparisonChartProps
> = ({ data }) => {
  // Combine data for comparison
  const combinedData = data.businessUnits
    .filter((unit) => unit.id !== "admin")
    .map((unit) => {
      const unitName = unit.name
      const profitData = data.profitability.find(
        (item) => item.unit === unitName
      )
      const satisfactionData = data.patientSatisfaction.find(
        (item) => item.unit === unitName
      )

      return {
        unit: unitName,
        profit: profitData?.profit || 0,
        margin: profitData?.margin || 0,
        satisfaction: satisfactionData?.score || 0,
        color: unit.color,
      }
    })

  // Sort by profit
  combinedData.sort((a, b) => b.profit - a.profit)

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={combinedData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="unit" tick={{ fontSize: 12 }} />
        <YAxis
          yAxisId="left"
          tickFormatter={(value) => `$${formatAbbreviatedCurrency(value, 0)}`}
          label={{
            value: "Profit ($)",
            angle: -90,
            position: "insideLeft",
            fontSize: 12,
          }}
          tick={{ fontSize: 12 }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          domain={[0, 5]}
          label={{
            value: "Patient Satisfaction (1-5)",
            angle: 90,
            position: "insideRight",
            fontSize: 12,
          }}
          tick={{ fontSize: 12 }}
        />
        <YAxis yAxisId="right-2" hide />
        <Tooltip
          formatter={(value, name) => {
            if (name === "Profit")
              return [`$${formatAbbreviatedCurrency(Number(value), 0)}`, name]
            if (name === "Margin") return [`${value}`, name]
            if (name === "Patient Satisfaction") return [value, name]
            return [value, name]
          }}
        />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar yAxisId="left" dataKey="profit" name="Profit" fill="#48BB78" />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="satisfaction"
          name="Patient Satisfaction"
          stroke="#ff7300"
          strokeWidth={2}
        />
        <Line
          yAxisId="right-2"
          type="monotone"
          dataKey="margin"
          name="Margin"
          stroke="#413ea0"
          strokeWidth={2}
          unit="%"
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default BusinessUnitComparisonChart
