import { formatAbbreviatedCurrency } from "@/lib/number"

export const workingCapitalData = {
  months: [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ],
  components: [
    {
      name: "Accounts Receivable",
      data: [
        760000, 780000, 800000, 820000, 840000, 855000, 870000, 885000, 900000,
        915000, 930000, 945000,
      ],
      color: "#4299E1", // blue - matching accountsReceivableData
    },
    {
      name: "Inventory",
      data: [
        420000, 425000, 430000, 435000, 440000, 445000, 450000, 455000, 460000,
        465000, 470000, 475000,
      ],
      color: "#48BB78", // green - matching accountsReceivableData
    },
    {
      name: "Accounts Payable (negative)",
      data: [
        -380000, -385000, -390000, -395000, -400000, -405000, -410000, -415000,
        -420000, -425000, -430000, -435000,
      ],
      color: "#E53E3E", // red - matching accountsReceivableData
    },
  ],
  workingCapitalPercent: [
    18.2, 18.0, 17.8, 17.6, 17.4, 17.2, 17.0, 16.8, 16.6, 16.4, 16.2, 16.0,
  ],
  industryBenchmark: 15.5,
  targetPercent: 16.0,
  kpis: [
    {
      name: "Working Capital",
      value: `$${formatAbbreviatedCurrency(985000, 0)}`,
      change: -2.3,
      trend: "down",
      status: "positive",
    },
    {
      name: "WC as % of Revenue",
      value: "16.0%",
      change: -0.4,
      trend: "down",
      status: "positive",
    },
    {
      name: "Current Ratio",
      value: "2.1",
      change: 0.1,
      trend: "up",
      status: "positive",
    },
    {
      name: "Quick Ratio",
      value: "1.8",
      change: 0.2,
      trend: "up",
      status: "positive",
    },
  ],
}
