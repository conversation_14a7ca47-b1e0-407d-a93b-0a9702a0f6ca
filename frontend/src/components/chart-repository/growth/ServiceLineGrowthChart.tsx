import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Cell,
  LabelList,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for service line growth
const serviceLineGrowthData = [
  {
    name: "Primary Care",
    currentYear: 4.2,
    previousYear: 3.8,
    growthRate: 10.5,
    target: 4.5,
  },
  {
    name: "Cardiology",
    currentYear: 3.8,
    previousYear: 3.2,
    growthRate: 18.8,
    target: 3.5,
  },
  {
    name: "Orthopedics",
    currentYear: 5.1,
    previousYear: 4.2,
    growthRate: 21.4,
    target: 4.8,
  },
  {
    name: "Pediatrics",
    currentYear: 2.9,
    previousYear: 2.6,
    growthRate: 11.5,
    target: 3.0,
  },
  {
    name: "Oncology",
    currentYear: 4.5,
    previousYear: 3.5,
    growthRate: 28.6,
    target: 4.0,
  },
  {
    name: "Neurology",
    currentYear: 3.2,
    previousYear: 2.8,
    growthRate: 14.3,
    target: 3.2,
  },
  {
    name: "Mental Health",
    currentYear: 2.4,
    previousYear: 1.8,
    growthRate: 33.3,
    target: 2.5,
  },
  {
    name: "Dermatology",
    currentYear: 1.9,
    previousYear: 1.5,
    growthRate: 26.7,
    target: 2.0,
  },
]

interface ServiceLineGrowthChartProps {
  data?: typeof serviceLineGrowthData
}

const ServiceLineGrowthChart: React.FC<ServiceLineGrowthChartProps> = ({
  data = serviceLineGrowthData,
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : serviceLineGrowthData

  // Sort data by growth rate
  const sortedData = [...chartData].sort((a, b) => b.growthRate - a.growthRate)

  // Calculate metrics
  const totalCurrentYear = chartData.reduce(
    (sum: number, item: any) => sum + item.currentYear,
    0
  )
  const totalPreviousYear = chartData.reduce(
    (sum: number, item: any) => sum + item.previousYear,
    0
  )
  const totalGrowth = totalCurrentYear - totalPreviousYear
  const totalGrowthPercent = Math.round((totalGrowth / totalPreviousYear) * 100)

  // Find highest growth service line
  const highestGrowth = sortedData[0]

  // Count how many service lines met or exceeded targets
  const metTargetCount = chartData.filter(
    (item: any) => item.currentYear >= item.target
  ).length
  const metTargetPercent = Math.round((metTargetCount / chartData.length) * 100)

  // Colors for the bars based on whether target was met
  const getBarColor = (currentYear: number, target: number) => {
    return currentYear >= target ? "#4ade80" : "#fb923c"
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Total Growth: +{totalGrowthPercent}%
          </div>
          <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Target Met: {metTargetPercent}%
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <BarChart data={chartData} layout="vertical">
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            type="number"
            domain={[0, "dataMax + 0.5"]}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            type="category"
            dataKey="name"
            width={100}
            tick={{ fontSize: 12 }}
          />
          <Tooltip
            formatter={(value, name) => [
              `$${formatCurrency(value as number, 0)}M`,
              name,
            ]}
          />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar dataKey="previousYear" name="Previous Year" fill="#94a3b8" />
          <Bar dataKey="currentYear" name="Current Year">
            {chartData.map((entry: any, index: number) => (
              <Cell
                key={`cell-${index}`}
                fill={getBarColor(entry.currentYear, entry.target)}
              />
            ))}
            <LabelList
              dataKey="growthRate"
              position="right"
              formatter={(value: any) => {
                if (typeof value === "number") return `+${value}%`
                return String(value)
              }}
              style={{ fontSize: 12, fill: "#374151" }}
            />
          </Bar>
          <Bar dataKey="target" name="Target" fill="#ef4444" />
        </BarChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2">
        <div className="rounded bg-blue-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Highest Growth</p>
          <p className="text-lg font-bold text-blue-600">
            {highestGrowth.name}
          </p>
          <p className="text-xs text-blue-600">+{highestGrowth.growthRate}%</p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Target Met</p>
          <p className="text-lg font-bold text-green-600">
            {metTargetCount}/{chartData.length}
          </p>
          <p className="text-xs text-green-600">Service Lines</p>
        </div>
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-700">Total Revenue</p>
          <p className="text-lg font-bold text-purple-600">
            ${totalCurrentYear.toFixed(1)}M
          </p>
          <p className="text-xs text-purple-600">+{totalGrowthPercent}% YoY</p>
        </div>
      </div>
    </div>
  )
}

export default ServiceLineGrowthChart
