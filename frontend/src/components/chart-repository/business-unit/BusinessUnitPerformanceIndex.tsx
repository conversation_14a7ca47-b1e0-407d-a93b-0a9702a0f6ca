import React from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface BusinessUnitPerformanceIndexProps {
  data: {
    revenueVsUtilization: Array<{
      unit: string
      revenue: number
      utilizationRate: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const BusinessUnitPerformanceIndex: React.FC<
  BusinessUnitPerformanceIndexProps
> = ({ data }) => {
  // Prepare data for Revenue vs Utilization chart
  const chartData = data.businessUnits
    .filter((unit) => unit.id !== "admin") // Exclude admin
    .map((unit) => {
      const unitName = unit.name

      // Get revenue and utilization data for this unit
      const revenueUtilData = data.revenueVsUtilization.find(
        (item) => item.unit === unitName
      )

      // Skip if data is missing
      if (!revenueUtilData) {
        return null
      }

      return {
        unit: unitName,
        revenue: revenueUtilData.revenue,
        utilizationRate: revenueUtilData.utilizationRate,
        color: unit.color,
      }
    })
    .filter(Boolean) // Remove null entries
    .sort((a, b) => b!.revenue - a!.revenue) // Sort by revenue

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart
        data={chartData as any[]}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="unit" tick={{ fontSize: 12 }} />
        <YAxis
          yAxisId="revenue"
          orientation="left"
          tick={{ fontSize: 12 }}
          tickFormatter={(value) => `$${formatAbbreviatedCurrency(value, 0)}`}
        />
        <YAxis
          yAxisId="utilization"
          orientation="right"
          tick={{ fontSize: 12 }}
          tickFormatter={(value) => `${value}%`}
          domain={[0, 100]}
        />
        <Tooltip
          formatter={(value, name) => {
            if (name === "Revenue") {
              return [`$${formatAbbreviatedCurrency(value as number, 0)}`, name]
            }
            return [`${value}%`, name]
          }}
          labelFormatter={(label) => `Business Unit: ${label}`}
        />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar
          yAxisId="revenue"
          dataKey="revenue"
          name="Revenue"
          fill="#48BB78"
        />
        <Line
          yAxisId="utilization"
          type="monotone"
          dataKey="utilizationRate"
          stroke="#413ea0"
          strokeWidth={2}
          name="Utilization Rate"
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default BusinessUnitPerformanceIndex
