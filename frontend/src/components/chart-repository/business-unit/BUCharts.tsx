"use client"

import React from "react"

import BusinessUnitComparisonChart from "@/components/chart-repository/business-unit/BusinessUnitComparisonChart"
import BusinessUnitGrowthChart from "@/components/chart-repository/business-unit/BusinessUnitGrowthChart"
import BusinessUnitPatientVolumeChart from "@/components/chart-repository/business-unit/BusinessUnitPatientVolumeChart"
import BusinessUnitPerformanceIndex from "@/components/chart-repository/business-unit/BusinessUnitPerformanceIndex"
import BusinessUnitProfitabilityChart from "@/components/chart-repository/business-unit/BusinessUnitProfitabilityChart"
import BusinessUnitRevenueChart from "@/components/chart-repository/business-unit/BusinessUnitRevenueChart"
import OperationalMetricsChart from "@/components/chart-repository/business-unit/OperationalMetricsChart"
import PatientSatisfactionChart from "@/components/chart-repository/business-unit/PatientSatisfactionChart"
import PayerMix<PERSON>hart from "@/components/chart-repository/business-unit/PayerMixChart"
import ProviderProductivityChart from "@/components/chart-repository/business-unit/ProviderProductivityChart"
import QualityMetricsChart from "@/components/chart-repository/business-unit/QualityMetricsChart"
import ReferralPatternsChart from "@/components/chart-repository/business-unit/ReferralPatternsChart"
import StaffUtilizationChart from "@/components/chart-repository/business-unit/StaffUtilizationChart"
import { Card } from "@/components/chart-repository/Card"
import { businessUnitPerformanceData } from "@/components/chart-repository/data/businessUnitPerformanceData"

const CHARTS = [
  {
    id: "BU-01",
    name: "Patient Volume by Business Unit",
    component: (
      <BusinessUnitPatientVolumeChart data={businessUnitPerformanceData} />
    ),
  },
  {
    id: "BU-02",
    name: "Revenue by Business Unit",
    component: <BusinessUnitRevenueChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-03",
    name: "Profitability by Business Unit",
    component: (
      <BusinessUnitProfitabilityChart data={businessUnitPerformanceData} />
    ),
  },
  {
    id: "BU-04",
    name: "Patient Satisfaction by Business Unit",
    component: <PatientSatisfactionChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-05",
    name: "Provider Productivity by Business Unit",
    component: <ProviderProductivityChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-06",
    name: "Quality Metrics by Business Unit",
    component: <QualityMetricsChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-07",
    name: "Referral Patterns Between Business Units",
    component: <ReferralPatternsChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-08",
    name: "Operational Metrics by Business Unit",
    component: <OperationalMetricsChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-09",
    name: "Payer Mix by Business Unit",
    component: <PayerMixChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-10",
    name: "Staff Utilization by Business Unit",
    component: <StaffUtilizationChart data={businessUnitPerformanceData} />,
  },
  {
    id: "BU-11",
    name: "Business Unit Comparison",
    component: (
      <BusinessUnitComparisonChart data={businessUnitPerformanceData} />
    ),
  },
  {
    id: "BU-12",
    name: "Further Revenue Opportunities",
    component: (
      <BusinessUnitPerformanceIndex data={businessUnitPerformanceData} />
    ),
  },
  {
    id: "BU-13",
    name: "Business Unit Growth Matrix",
    component: <BusinessUnitGrowthChart data={businessUnitPerformanceData} />,
  },
]

const BUCharts = () => (
  <div className="grid gap-4 lg:grid-cols-2">
    {CHARTS.map((chart, index) => (
      <Card key={index} id={chart.id} title={chart.name}>
        {chart.component}
      </Card>
    ))}
  </div>
)

export default BUCharts
