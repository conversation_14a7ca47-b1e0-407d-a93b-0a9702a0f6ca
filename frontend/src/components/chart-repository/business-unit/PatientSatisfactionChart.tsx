import React from "react"
import {
  <PERSON>,
  CartesianGrid,
  Composed<PERSON>hart,
  Legend,
  Line,
  ReferenceLine,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

interface PatientSatisfactionChartProps {
  data: {
    patientSatisfaction: Array<{
      unit: string
      score: number
      benchmark: number
      responses: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const PatientSatisfactionChart: React.FC<PatientSatisfactionChartProps> = ({
  data,
}) => {
  // Calculate average benchmark
  const avgBenchmark =
    data.patientSatisfaction.reduce((sum, item) => sum + item.benchmark, 0) /
    data.patientSatisfaction.length

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={data.patientSatisfaction}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="unit" tick={{ fontSize: 12 }} />
        <YAxis
          yAxisId="left"
          domain={[0, 5]}
          label={{
            value: "Satisfaction Score (1-5)",
            angle: -90,
            position: "insideLeft",
            fontSize: 12,
          }}
          tick={{ fontSize: 12 }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          domain={[0, "dataMax + 200"]}
          label={{
            value: "Responses",
            angle: 90,
            position: "insideRight",
            fontSize: 12,
          }}
          tick={{ fontSize: 12 }}
        />
        <Tooltip />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar
          yAxisId="left"
          dataKey="score"
          name="Patient Satisfaction"
          fill="#82ca9d"
        />
        <Line
          yAxisId="left"
          type="monotone"
          dataKey="benchmark"
          name="Benchmark"
          stroke="#000000"
          strokeWidth={2}
          strokeDasharray="5 5"
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="responses"
          name="Survey Responses"
          stroke="#8884d8"
          strokeWidth={2}
        />
        <ReferenceLine
          yAxisId="left"
          y={avgBenchmark}
          stroke="#FF0000"
          strokeDasharray="3 3"
          label={{
            value: "Avg Benchmark",
            position: "insideBottomRight",
            fontSize: 12,
          }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default PatientSatisfactionChart
