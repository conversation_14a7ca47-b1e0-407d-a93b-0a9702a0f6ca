// Cumulative net profit data for 2023, 2024, and Jan-May 2025
export interface CumulativeNetProfitData {
  date: string // Format: YYYY-MM
  month: string // Format: MMM (e.g., Jan, Feb)
  year: number
  revenue: number
  expenses: number
  netProfit: number
  cumulativeNetProfit: number
}

// Generate realistic cumulative net profit data
export const generateCumulativeNetProfitData = (): {
  data: CumulativeNetProfitData[]
} => {
  const data: CumulativeNetProfitData[] = []
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ]
  const years = [2023, 2024, 2025]

  // Base revenue starting point (in millions)
  let baseRevenue = 2.5

  // Generate data for each month in the specified years
  years.forEach((year) => {
    let cumulativeNetProfit = 0 // Reset for each year

    // Only include Jan-May for 2025
    const monthsToInclude = year === 2025 ? months.slice(0, 5) : months

    monthsToInclude.forEach((month, monthIndex) => {
      // Add some seasonality and growth
      const seasonality = 1 + Math.sin((monthIndex / 11) * Math.PI) * 0.2
      const yearGrowth = year === 2023 ? 1 : year === 2024 ? 1.15 : 1.25 // 15% growth in 2024, 25% in 2025

      // Add some randomness
      const randomFactor = 0.9 + Math.random() * 0.3 // Random factor between 0.9 and 1.2

      // Calculate revenue for this month (in millions)
      const revenue =
        baseRevenue * seasonality * yearGrowth * randomFactor * 1000000

      // Calculate expenses (70-85% of revenue with some randomness)
      const expenseRatio = 0.7 + Math.random() * 0.15
      const expenses = revenue * expenseRatio

      // Calculate net profit
      const netProfit = revenue - expenses

      // Update cumulative net profit (reset yearly)
      cumulativeNetProfit += netProfit

      // Format the date as YYYY-MM
      const monthNum = (monthIndex + 1).toString().padStart(2, "0")
      const date = `${year}-${monthNum}`

      data.push({
        date,
        month,
        year,
        revenue,
        expenses,
        netProfit,
        cumulativeNetProfit,
      })
    })
  })

  return { data }
}

// Export the cumulative net profit data
export const cumulativeNetProfitData = generateCumulativeNetProfitData()
