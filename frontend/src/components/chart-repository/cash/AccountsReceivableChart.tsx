import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Composed<PERSON><PERSON>,
  Legend,
  Line,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface AccountsReceivableChartProps {
  data: {
    months: string[]
    agingBuckets: {
      name: string
      data: number[]
      color: string
    }[]
    topCustomers: {
      name: string
      current: number
      "0-30": number
      "31-60": number
      "61-90": number
      "90+": number
    }[]
  }
}

const AccountsReceivableChart: React.FC<AccountsReceivableChartProps> = ({
  data,
}) => {
  // Transform data for Recharts
  const transformedData = data.months.map((month, index) => {
    const result: any = { month }

    // Add each aging bucket's data for this month
    data.agingBuckets.forEach((bucket) => {
      result[bucket.name] = bucket.data[index]
    })

    return result
  })

  // Custom tooltip for aging buckets chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Calculate total for this month
      const total = payload.reduce(
        (sum: number, entry: any) => sum + (entry.value || 0),
        0
      )

      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <p className="font-semibold">{label}</p>
          {payload.map((entry: any, index: number) => {
            const percentage =
              total > 0 ? ((entry.value / total) * 100).toFixed(1) : "0.0"
            return (
              <p key={index} style={{ color: entry.color }}>
                {entry.name}:{" "}
                {formatAbbreviatedCurrency(entry.value as number, 0)}
                {entry.name === "Total" ? "" : ` (${percentage}%)`}
              </p>
            )
          })}
        </div>
      )
    }
    return null
  }

  // Custom tooltip for top customers chart
  const CustomCustomerTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Calculate total for this customer
      const total = payload.reduce(
        (sum: number, entry: any) => sum + (entry.value || 0),
        0
      )

      return (
        <div className="rounded-md border border-gray-200 bg-white p-2 shadow-lg">
          <p className="font-semibold">{label}</p>
          {payload.map((entry: any, index: number) => {
            const percentage =
              total > 0 ? ((entry.value / total) * 100).toFixed(1) : "0.0"
            return (
              <p key={index} style={{ color: entry.color }}>
                {entry.name}:{" "}
                {formatAbbreviatedCurrency(entry.value as number, 0)}
                {entry.name === "Total" ? "" : ` (${percentage}%)`}
              </p>
            )
          })}
        </div>
      )
    }
    return null
  }

  return (
    <div className="flex flex-col">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT / 2}>
        <BarChart data={transformedData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis
            tickFormatter={(value) => `$${formatAbbreviatedCurrency(value, 0)}`}
            tick={{ fontSize: 12 }}
          />
          <RechartsTooltip content={<CustomTooltip />} />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 8 }}
          />

          {data.agingBuckets.map((bucket, index) => (
            <Bar
              key={`bar-${index}`}
              dataKey={bucket.name}
              stackId="a"
              fill={bucket.color}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT / 2}>
        <ComposedChart
          data={data.topCustomers.map((customer) => ({
            name: customer.name,
            current: customer.current,
            "0-30": customer["0-30"],
            "31-60": customer["31-60"],
            "61-90": customer["61-90"],
            "90+": customer["90+"],
            total:
              customer.current +
              customer["0-30"] +
              customer["31-60"] +
              customer["61-90"] +
              customer["90+"],
          }))}
          layout="vertical"
        >
          <CartesianGrid strokeDasharray="3 3" horizontal={false} />
          <XAxis type="number" hide />
          <YAxis
            dataKey="name"
            type="category"
            tick={{ fontSize: 12 }}
            width={80}
          />
          <Tooltip content={<CustomCustomerTooltip />} />
          <Bar dataKey="current" stackId="a" fill="#38A169" name="Current" />
          <Bar dataKey="0-30" stackId="a" fill="#48BB78" name="0-30 Days" />
          <Bar dataKey="31-60" stackId="a" fill="#4299E1" name="31-60 Days" />
          <Bar dataKey="61-90" stackId="a" fill="#ED8936" name="61-90 Days" />
          <Bar dataKey="90+" stackId="a" fill="#E53E3E" name="90+ Days" />
          <Legend wrapperStyle={{ fontSize: 12, paddingTop: 8 }} />
          {/* Add a line for total amount */}
          <Line
            type="monotone"
            dataKey="total"
            stroke="#805AD5"
            strokeWidth={2}
            dot={{ r: 3 }}
            name="Total"
            yAxisId={0}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  )
}

export default AccountsReceivableChart
