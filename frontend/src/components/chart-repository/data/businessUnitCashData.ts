export const businessUnitCashData = {
  // Data for treemap showing cash generation by business unit
  cashGeneration: [
    {
      name: 'Primary Care',
      value: 450000,
      color: '#4299E1',
      percentChange: 8.5
    },
    {
      name: 'Specialty Care',
      value: 380000,
      color: '#48BB78',
      percentChange: 12.3
    },
    {
      name: 'Urgent Care',
      value: 320000,
      color: '#ED8936',
      percentChange: 5.2
    },
    {
      name: 'Diagnostics',
      value: 280000,
      color: '#9F7AEA',
      percentChange: 7.8
    },
    {
      name: 'Surgery',
      value: 250000,
      color: '#F56565',
      percentChange: -2.1
    },
    {
      name: 'Physical Therapy',
      value: 180000,
      color: '#38B2AC',
      percentChange: 9.4
    },
    {
      name: 'Mental Health',
      value: 150000,
      color: '#ECC94B',
      percentChange: 15.7
    },
    {
      name: 'Administration',
      value: -120000,
      color: '#A0AEC0',
      percentChange: 3.2
    }
  ],
  
  // Data for pie chart showing cash usage by category
  cashUsage: [
    {
      name: 'Staffing',
      value: 420000,
      color: '#4299E1' // Corresponds to 'Supplies' category color
    },
    {
      name: 'Supplies',
      value: 280000,
      color: '#805AD5' // Corresponds to 'Equipment' category color
    },
    {
      name: 'Equipment',
      value: 180000,
      color: '#38B2AC' // Corresponds to 'Facilities' category color
    },
    {
      name: 'Facilities',
      value: 150000,
      color: '#F6AD55' // Corresponds to 'Insurance' category color
    },
    {
      name: 'IT Systems',
      value: 120000,
      color: '#FC8181' // Corresponds to 'Pharmaceuticals' category color
    },
    {
      name: 'Marketing',
      value: 90000,
      color: '#9AE6B4' // Corresponds to 'Lab Services' category color
    },
    {
      name: 'Training',
      value: 60000,
      color: '#90CDF4' // Corresponds to 'IT Services' category color
    },
    {
      name: 'Other',
      value: 100000,
      color: '#FBD38D' // Corresponds to 'Marketing' category color
    }
  ]
};
